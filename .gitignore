##### Environment files
# Keep secrets and local env config out of VCS
.env.*
**/.env.*

##### Flutter / Dart / Pub
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub/
build/
**/build/
doc/api/
coverage/
**/pubspec_overrides.yaml

# Generated plugin registrant for web
**/lib/generated_plugin_registrant.dart

##### IDEs / Editors
.idea/
.vscode/
*.iml
*.ipr
*.iws

##### OS files
.DS_Store
Thumbs.db

##### Android
**/android/.gradle/
**/android/local.properties
**/android/captures/
**/android/app/debug.keystore
**/android/key.properties
**/android/app/*.jks
**/android/app/*.keystore
# Generated by Flutter toolchain
**/android/**/GeneratedPluginRegistrant.java
# Flutter plugin/module hidden Android wrapper
.android/

##### iOS
**/ios/Flutter/.last_build_id
**/ios/Flutter/Flutter.framework
**/ios/Flutter/Flutter.podspec
**/ios/Flutter/Generated.xcconfig
**/ios/Flutter/app.flx
**/ios/Flutter/app.zip
**/ios/Flutter/flutter_assets/
**/ios/Flutter/flutter_export_environment.sh
**/ios/ServiceDefinitions.json
**/ios/Runner/GeneratedPluginRegistrant.*
**/ios/Pods/
**/ios/**/xcuserdata/
**/ios/**/DerivedData/
# Flutter plugin/module hidden iOS wrapper
.ios/
**/.symlinks/

##### macOS
**/macos/Flutter/ephemeral/
**/macos/Flutter/GeneratedPluginRegistrant.swift
**/macos/Flutter/Flutter-Debug.xcconfig
**/macos/Flutter/Flutter-Release.xcconfig
**/macos/Flutter/Flutter-Profile.xcconfig
**/macos/Pods/
**/macos/**/xcuserdata/
**/macos/**/DerivedData/

##### Linux
**/linux/flutter/ephemeral/
**/linux/**/generated_plugin_registrant.*
**/linux/**/generated_plugins.cmake

##### Windows
**/windows/flutter/ephemeral/
**/windows/**/generated_plugin_registrant.*
**/windows/flutter/generated_plugins.cmake

##### Misc
*.log
*.tmp
*.swp
*.bak
*.orig
.svn/
*.apk
*.aab
*.ipa
*.app

##### FVM (Flutter Version Manager)
.fvm/
!.fvm/fvm_config.json


