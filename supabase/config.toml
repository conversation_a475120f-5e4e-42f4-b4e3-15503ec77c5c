# Supabase configuration for multi-vendor ecommerce platform

project_id = "multi-vendor-ecommerce"

[api]
enabled = true
port = 54321
schemas = ["public", "graphql_public"]
extra_search_path = ["public", "extensions"]
max_rows = 1000

[auth]
enabled = true
port = 54322
site_url = "http://localhost:3000"
additional_redirect_urls = ["https://localhost:3000"]
jwt_expiry = 3600
refresh_token_rotation_enabled = true
refresh_token_reuse_interval = 10
enable_signup = true
enable_email_confirmations = false
enable_email_change_confirmations = true
enable_phone_confirmations = false
enable_phone_change_confirmations = true

[auth.email]
enable_signup = true
double_confirm_changes = true
enable_confirmations = false
template = {}

[auth.sms]
enable_signup = false
enable_confirmations = false
template = "Your code is {{ .Code }}"

[auth.external.apple]
enabled = false
client_id = ""
secret = ""
redirect_uri = ""

[auth.external.azure]
enabled = false
client_id = ""
secret = ""
redirect_uri = ""

[auth.external.bitbucket]
enabled = false
client_id = ""
secret = ""
redirect_uri = ""

[auth.external.discord]
enabled = false
client_id = ""
secret = ""
redirect_uri = ""

[auth.external.facebook]
enabled = false
client_id = ""
secret = ""
redirect_uri = ""

[auth.external.github]
enabled = false
client_id = ""
secret = ""
redirect_uri = ""

[auth.external.gitlab]
enabled = false
client_id = ""
secret = ""
redirect_uri = ""

[auth.external.google]
enabled = false
client_id = ""
secret = ""
redirect_uri = ""

[auth.external.keycloak]
enabled = false
client_id = ""
secret = ""
redirect_uri = ""
url = ""

[auth.external.linkedin]
enabled = false
client_id = ""
secret = ""
redirect_uri = ""

[auth.external.notion]
enabled = false
client_id = ""
secret = ""
redirect_uri = ""

[auth.external.twitch]
enabled = false
client_id = ""
secret = ""
redirect_uri = ""

[auth.external.twitter]
enabled = false
client_id = ""
secret = ""
redirect_uri = ""

[auth.external.slack]
enabled = false
client_id = ""
secret = ""
redirect_uri = ""

[auth.external.spotify]
enabled = false
client_id = ""
secret = ""
redirect_uri = ""

[auth.external.workos]
enabled = false
client_id = ""
secret = ""
redirect_uri = ""
url = ""

[auth.external.zoom]
enabled = false
client_id = ""
secret = ""
redirect_uri = ""

[db]
port = 54322
shadow_port = 54320
major_version = 15

[db.pooler]
enabled = false
port = 54329
pool_mode = "transaction"
default_pool_size = 20
max_client_conn = 100

[realtime]
enabled = true
port = 54323
ip_version = "ipv4"

[studio]
enabled = true
port = 54323
api_url = "http://localhost:54321"

[inbucket]
enabled = true
port = 54324
smtp_port = 54325
pop3_port = 54326

[storage]
enabled = true
port = 54325
file_size_limit = "50MiB"
image_transformation = {enabled = true}

[analytics]
enabled = false
port = 54327
vector_port = 54328
gcp_project_id = ""
gcp_project_number = ""
gcp_jwt_path = "supabase/gcp.json"

[functions]
verify_jwt = false

[edge_runtime]
enabled = true
port = 54326
inspector_port = 54327