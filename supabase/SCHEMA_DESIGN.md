# Database Schema Design Documentation

## Overview

This document explains the design decisions and rationale behind the database schema for the multi-vendor ecommerce platform.

## Design Principles

### 1. Security First
- **Row Level Security (RLS)** enabled on all tables
- **Principle of least privilege** - users can only access their own data
- **Data isolation** between different user types (customers, vendors, couriers)
- **Input validation** through check constraints and functions

### 2. Arabic Language Support
- All user-facing text fields use `VARCHAR` and `TEXT` types with UTF-8 support
- Product names and descriptions stored in Arabic (`name_ar`, `description_ar`)
- Proper indexing for Arabic text search and filtering
- RTL (Right-to-Left) text direction considerations in application layer

### 3. Performance Optimization
- **Strategic indexing** on frequently queried columns
- **Spatial indexing** for location-based courier assignment
- **Composite indexes** for complex queries
- **Lazy loading** support through pagination functions

### 4. Data Integrity
- **Foreign key constraints** maintain referential integrity
- **Check constraints** ensure data validity (prices ≥ 0, ratings 0-5)
- **Triggers** maintain calculated fields automatically
- **Atomic operations** through stored procedures

## Table Design Decisions

### User Management

#### Users Table (Base Table)
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR UNIQUE NOT NULL,
  name VARCHAR NOT NULL,
  user_type user_type_enum NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Design Rationale:**
- **Single table inheritance** pattern for all user types
- **UUID primary keys** for better security and distribution
- **Email uniqueness** enforced at database level
- **User type enum** prevents invalid user types
- **Automatic timestamps** for audit trails

#### Profile Tables (Customers, Vendors, Couriers)
Each user type has a dedicated profile table that references the base users table.

**Benefits:**
- **Type-specific fields** without null columns in base table
- **Flexible schema evolution** for each user type
- **Clear separation of concerns**
- **Efficient queries** for type-specific operations

### Product Management

#### Products Table
```sql
CREATE TABLE products (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  vendor_id UUID NOT NULL REFERENCES vendors(id) ON DELETE CASCADE,
  name_ar VARCHAR NOT NULL,
  description_ar TEXT,
  price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
  stock_quantity INTEGER NOT NULL DEFAULT 0 CHECK (stock_quantity >= 0),
  category product_category_enum NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  weight_grams INTEGER,
  dimensions_cm JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

**Design Rationale:**
- **Arabic-first naming** (`name_ar`, `description_ar`)
- **Decimal precision** for accurate price calculations
- **Check constraints** prevent negative values
- **JSONB for dimensions** allows flexible structure
- **Soft delete** via `is_active` flag
- **Vendor ownership** enforced through foreign key

#### Product Images Table
Separate table for product images to support multiple images per product.

**Benefits:**
- **Multiple images** per product
- **BlurHash support** for smooth loading
- **Ordered display** via `sort_order`
- **Image metadata** (width, height) for optimization

### Order Management

#### Orders Table
```sql
CREATE TABLE orders (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id UUID NOT NULL REFERENCES customers(id),
  vendor_id UUID NOT NULL REFERENCES vendors(id),
  courier_id UUID REFERENCES couriers(id),
  total_amount DECIMAL(10,2) NOT NULL CHECK (total_amount >= 0),
  status order_status_enum NOT NULL DEFAULT 'pending',
  delivery_address JSONB NOT NULL,
  payment_method payment_method_enum NOT NULL DEFAULT 'cash_on_delivery',
  -- ... other fields
);
```

**Design Rationale:**
- **Multi-party relationship** (customer, vendor, courier)
- **Flexible address storage** using JSONB
- **Cash-only payment** enforced by enum
- **Status tracking** with enum constraints
- **Nullable courier** (assigned later in workflow)

#### Order Items Table
Normalized design separating order header from line items.

**Benefits:**
- **Normalized structure** reduces data duplication
- **Historical pricing** preserved in order items
- **Flexible quantity handling**
- **Easy reporting** and analytics

#### Order Status History Table
Audit trail for all order status changes.

**Benefits:**
- **Complete audit trail** of order lifecycle
- **User attribution** for status changes
- **Debugging support** for order issues
- **Analytics data** for process optimization

### Location and Delivery

#### Addresses Table
```sql
CREATE TABLE addresses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id UUID NOT NULL REFERENCES customers(id) ON DELETE CASCADE,
  label VARCHAR NOT NULL, -- 'Home', 'Work', etc.
  -- ... address fields
  location POINT,
  is_default BOOLEAN DEFAULT FALSE,
  -- ... timestamps
);
```

**Design Rationale:**
- **Multiple addresses** per customer
- **Structured address fields** for Arabic addresses
- **PostGIS Point type** for geolocation
- **Default address** support
- **Labeled addresses** for user convenience

#### Courier Location Tracking
Couriers table includes `current_location` field for real-time tracking.

**Benefits:**
- **Real-time location** updates
- **Spatial queries** for courier assignment
- **Distance calculations** for delivery optimization
- **Availability tracking** integrated with location

## Security Model

### Row Level Security (RLS) Policies

#### Customer Policies
- Customers can only view their own data
- Customers can view active products from verified vendors
- Customers can create and view their own orders

#### Vendor Policies
- Vendors can manage their own products and inventory
- Vendors can view and update orders for their products
- Vendors cannot access other vendors' data

#### Courier Policies
- Couriers can view assigned delivery orders
- Couriers can update delivery status
- Couriers cannot access unassigned orders

### Function Security
All stored procedures use `SECURITY DEFINER` to ensure proper privilege escalation while maintaining security boundaries.

## Performance Considerations

### Indexing Strategy

#### Primary Indexes
- **B-tree indexes** on frequently queried columns
- **Composite indexes** for multi-column queries
- **Partial indexes** for filtered queries (e.g., active products only)

#### Spatial Indexes
- **GiST indexes** on location columns for spatial queries
- **Optimized courier assignment** based on proximity

#### Text Search Indexes
- **GIN indexes** for full-text search on Arabic content
- **Trigram indexes** for fuzzy matching

### Query Optimization

#### Stored Procedures
Complex queries encapsulated in stored procedures for:
- **Consistent performance** across applications
- **Reduced network traffic**
- **Centralized business logic**
- **Security through controlled access**

#### Pagination Support
All list queries support pagination to handle large datasets efficiently.

## Data Integrity Mechanisms

### Triggers

#### Automatic Profile Creation
```sql
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();
```
Automatically creates appropriate profile records when users are created.

#### Order Total Maintenance
```sql
CREATE TRIGGER update_order_total_on_insert
  AFTER INSERT ON order_items
  FOR EACH ROW EXECUTE FUNCTION update_order_total();
```
Maintains order totals automatically when items are added/removed.

#### Status Change Logging
```sql
CREATE TRIGGER log_order_status_changes
  AFTER UPDATE ON orders
  FOR EACH ROW EXECUTE FUNCTION log_order_status_change();
```
Logs all order status changes for audit purposes.

### Constraints

#### Check Constraints
- Prices and quantities must be non-negative
- Ratings must be between 0 and 5
- Stock quantities cannot be negative

#### Foreign Key Constraints
- Maintain referential integrity across all relationships
- Cascade deletes where appropriate
- Prevent orphaned records

## Scalability Considerations

### Horizontal Scaling
- **UUID primary keys** support distributed systems
- **Partitioning ready** table designs
- **Stateless functions** for easy replication

### Vertical Scaling
- **Efficient indexing** reduces I/O requirements
- **Normalized design** minimizes storage overhead
- **Query optimization** through stored procedures

### Caching Strategy
- **Immutable data** (completed orders) can be cached indefinitely
- **Product data** cached with TTL based on update frequency
- **User sessions** cached for authentication performance

## Monitoring and Maintenance

### Performance Monitoring
- **Query execution plans** tracked for optimization
- **Index usage statistics** monitored for efficiency
- **Connection pooling** configured for optimal resource usage

### Data Maintenance
- **Automated backups** configured through Supabase
- **Archive strategy** for old orders and logs
- **Index maintenance** scheduled during low-traffic periods

### Security Auditing
- **RLS policy effectiveness** monitored
- **Authentication logs** reviewed regularly
- **Data access patterns** analyzed for anomalies

## Future Enhancements

### Planned Features
1. **Multi-language support** (English alongside Arabic)
2. **Advanced search** with Elasticsearch integration
3. **Real-time notifications** using Supabase Realtime
4. **Analytics dashboard** with pre-computed metrics
5. **Mobile push notifications** integration

### Schema Evolution
- **Migration strategy** for schema changes
- **Backward compatibility** considerations
- **Feature flags** for gradual rollouts
- **A/B testing** support in data model

This schema design provides a solid foundation for the multi-vendor ecommerce platform while maintaining flexibility for future enhancements and ensuring optimal performance and security.