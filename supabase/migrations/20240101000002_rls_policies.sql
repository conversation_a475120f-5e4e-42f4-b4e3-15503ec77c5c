-- Row Level Security (RLS) policies for multi-vendor ecommerce platform
-- This migration enables R<PERSON> and creates security policies for all tables

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE vendors ENABLE ROW LEVEL SECURITY;
ALTER TABLE couriers ENABLE ROW LEVEL SECURITY;
ALTER TABLE addresses ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_images ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_status_history ENABLE ROW LEVEL SECURITY;

-- Users table policies
CREATE POLICY "Users can view their own data" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own data" ON users
  FOR UPDATE USING (auth.uid() = id);

-- Customers table policies
CREATE POLICY "Customers can view their own data" ON customers
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Customers can update their own data" ON customers
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "New customers can insert their data" ON customers
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Vendors table policies
CREATE POLICY "Vendors can view their own data" ON vendors
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Vendors can update their own data" ON vendors
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "New vendors can insert their data" ON vendors
  FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Customers can view verified vendors" ON vendors
  FOR SELECT USING (is_verified = true);

-- Couriers table policies
CREATE POLICY "Couriers can view their own data" ON couriers
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Couriers can update their own data" ON couriers
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "New couriers can insert their data" ON couriers
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Addresses table policies
CREATE POLICY "Customers can manage their own addresses" ON addresses
  FOR ALL USING (
    customer_id IN (
      SELECT id FROM customers WHERE id = auth.uid()
    )
  );

CREATE POLICY "Vendors and couriers can view delivery addresses for their orders" ON addresses
  FOR SELECT USING (
    customer_id IN (
      SELECT customer_id FROM orders 
      WHERE vendor_id = auth.uid() OR courier_id = auth.uid()
    )
  );

-- Products table policies
CREATE POLICY "Vendors can manage their own products" ON products
  FOR ALL USING (vendor_id = auth.uid());

CREATE POLICY "Everyone can view active products" ON products
  FOR SELECT USING (is_active = true);

-- Product images table policies
CREATE POLICY "Vendors can manage images for their products" ON product_images
  FOR ALL USING (
    product_id IN (
      SELECT id FROM products WHERE vendor_id = auth.uid()
    )
  );

CREATE POLICY "Everyone can view images for active products" ON product_images
  FOR SELECT USING (
    product_id IN (
      SELECT id FROM products WHERE is_active = true
    )
  );

-- Orders table policies
CREATE POLICY "Customers can view their own orders" ON orders
  FOR SELECT USING (customer_id = auth.uid());

CREATE POLICY "Customers can create orders" ON orders
  FOR INSERT WITH CHECK (customer_id = auth.uid());

CREATE POLICY "Vendors can view orders for their products" ON orders
  FOR SELECT USING (vendor_id = auth.uid());

CREATE POLICY "Vendors can update status of their orders" ON orders
  FOR UPDATE USING (vendor_id = auth.uid())
  WITH CHECK (vendor_id = auth.uid());

CREATE POLICY "Couriers can view assigned orders" ON orders
  FOR SELECT USING (courier_id = auth.uid());

CREATE POLICY "Couriers can update assigned orders" ON orders
  FOR UPDATE USING (courier_id = auth.uid())
  WITH CHECK (courier_id = auth.uid());

-- Order items table policies
CREATE POLICY "Customers can view items in their orders" ON order_items
  FOR SELECT USING (
    order_id IN (
      SELECT id FROM orders WHERE customer_id = auth.uid()
    )
  );

CREATE POLICY "Vendors can view items in their orders" ON order_items
  FOR SELECT USING (
    order_id IN (
      SELECT id FROM orders WHERE vendor_id = auth.uid()
    )
  );

CREATE POLICY "Couriers can view items in assigned orders" ON order_items
  FOR SELECT USING (
    order_id IN (
      SELECT id FROM orders WHERE courier_id = auth.uid()
    )
  );

CREATE POLICY "Customers can insert items when creating orders" ON order_items
  FOR INSERT WITH CHECK (
    order_id IN (
      SELECT id FROM orders WHERE customer_id = auth.uid()
    )
  );

-- Order status history table policies
CREATE POLICY "Users can view status history for their orders" ON order_status_history
  FOR SELECT USING (
    order_id IN (
      SELECT id FROM orders 
      WHERE customer_id = auth.uid() 
         OR vendor_id = auth.uid() 
         OR courier_id = auth.uid()
    )
  );

CREATE POLICY "Vendors and couriers can insert status updates" ON order_status_history
  FOR INSERT WITH CHECK (
    changed_by = auth.uid() AND
    order_id IN (
      SELECT id FROM orders 
      WHERE vendor_id = auth.uid() OR courier_id = auth.uid()
    )
  );

-- Additional security functions
CREATE OR REPLACE FUNCTION is_vendor(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM users 
    WHERE id = user_id AND user_type = 'vendor'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION is_courier(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM users 
    WHERE id = user_id AND user_type = 'courier'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION is_customer(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM users 
    WHERE id = user_id AND user_type = 'customer'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to automatically create user profile based on user_type
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Create corresponding profile based on user_type
  IF NEW.user_type = 'customer' THEN
    INSERT INTO customers (id) VALUES (NEW.id);
  ELSIF NEW.user_type = 'vendor' THEN
    INSERT INTO vendors (id, business_name, category, business_address) 
    VALUES (NEW.id, 'New Business', 'electronics', '{}');
  ELSIF NEW.user_type = 'courier' THEN
    INSERT INTO couriers (id, phone_number, vehicle_type) 
    VALUES (NEW.id, '', 'motorcycle');
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to automatically create user profiles
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Function to update order total when items change
CREATE OR REPLACE FUNCTION update_order_total()
RETURNS TRIGGER AS $$
DECLARE
  new_total DECIMAL(10,2);
BEGIN
  -- Calculate new total for the order
  SELECT COALESCE(SUM(total_price), 0) INTO new_total
  FROM order_items
  WHERE order_id = COALESCE(NEW.order_id, OLD.order_id);
  
  -- Update the order total
  UPDATE orders 
  SET total_amount = new_total, updated_at = NOW()
  WHERE id = COALESCE(NEW.order_id, OLD.order_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Triggers to maintain order totals
CREATE TRIGGER update_order_total_on_insert
  AFTER INSERT ON order_items
  FOR EACH ROW EXECUTE FUNCTION update_order_total();

CREATE TRIGGER update_order_total_on_update
  AFTER UPDATE ON order_items
  FOR EACH ROW EXECUTE FUNCTION update_order_total();

CREATE TRIGGER update_order_total_on_delete
  AFTER DELETE ON order_items
  FOR EACH ROW EXECUTE FUNCTION update_order_total();

-- Function to log order status changes
CREATE OR REPLACE FUNCTION log_order_status_change()
RETURNS TRIGGER AS $$
BEGIN
  -- Only log if status actually changed
  IF OLD.status IS DISTINCT FROM NEW.status THEN
    INSERT INTO order_status_history (order_id, status, changed_by, notes)
    VALUES (NEW.id, NEW.status, auth.uid(), 'Status updated');
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to log order status changes
CREATE TRIGGER log_order_status_changes
  AFTER UPDATE ON orders
  FOR EACH ROW EXECUTE FUNCTION log_order_status_change();