-- Helper functions for multi-vendor ecommerce platform
-- This migration adds utility functions for common operations

-- Function to get available couriers near a location
CREATE OR REPLACE FUNCTION get_available_couriers_near_location(
  target_lat FLOAT,
  target_lng FLOAT,
  radius_km FLOAT DEFAULT 10
)
RETURNS TABLE (
  courier_id UUID,
  courier_name VARCHAR,
  phone_number VARCHAR,
  vehicle_type VARCHAR,
  distance_km FLOAT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    c.id,
    u.name,
    c.phone_number,
    c.vehicle_type,
    ST_Distance(
      ST_GeogFromText('POINT(' || target_lng || ' ' || target_lat || ')'),
      ST_GeogFromText('POINT(' || ST_X(c.current_location) || ' ' || ST_Y(c.current_location) || ')')
    ) / 1000 as distance_km
  FROM couriers c
  JOIN users u ON c.id = u.id
  WHERE c.is_available = true
    AND c.current_location IS NOT NULL
    AND ST_DWithin(
      ST_GeogFromText('POINT(' || target_lng || ' ' || target_lat || ')'),
      ST_GeogFromText('POINT(' || ST_X(c.current_location) || ' ' || ST_Y(c.current_location) || ')'),
      radius_km * 1000
    )
  ORDER BY distance_km ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to search products with filters
CREATE OR REPLACE FUNCTION search_products(
  search_query TEXT DEFAULT '',
  category_filter product_category_enum DEFAULT NULL,
  vendor_id_filter UUID DEFAULT NULL,
  min_price DECIMAL DEFAULT 0,
  max_price DECIMAL DEFAULT NULL,
  limit_count INTEGER DEFAULT 20,
  offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
  product_id UUID,
  vendor_id UUID,
  vendor_name VARCHAR,
  name_ar VARCHAR,
  description_ar TEXT,
  price DECIMAL,
  stock_quantity INTEGER,
  category product_category_enum,
  rating DECIMAL,
  image_url VARCHAR,
  blur_hash VARCHAR
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id,
    p.vendor_id,
    v.business_name,
    p.name_ar,
    p.description_ar,
    p.price,
    p.stock_quantity,
    p.category,
    v.rating,
    pi.url,
    pi.blur_hash
  FROM products p
  JOIN vendors v ON p.vendor_id = v.id
  LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.sort_order = 0
  WHERE p.is_active = true
    AND v.is_verified = true
    AND (search_query = '' OR p.name_ar ILIKE '%' || search_query || '%' OR p.description_ar ILIKE '%' || search_query || '%')
    AND (category_filter IS NULL OR p.category = category_filter)
    AND (vendor_id_filter IS NULL OR p.vendor_id = vendor_id_filter)
    AND p.price >= min_price
    AND (max_price IS NULL OR p.price <= max_price)
    AND p.stock_quantity > 0
  ORDER BY v.rating DESC, p.created_at DESC
  LIMIT limit_count
  OFFSET offset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get order details with items
CREATE OR REPLACE FUNCTION get_order_details(order_id_param UUID)
RETURNS TABLE (
  order_id UUID,
  customer_id UUID,
  customer_name VARCHAR,
  customer_phone VARCHAR,
  vendor_id UUID,
  vendor_name VARCHAR,
  courier_id UUID,
  courier_name VARCHAR,
  courier_phone VARCHAR,
  total_amount DECIMAL,
  status order_status_enum,
  delivery_address JSONB,
  payment_method payment_method_enum,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE,
  estimated_delivery_time TIMESTAMP WITH TIME ZONE,
  items JSONB
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    o.id,
    o.customer_id,
    cu.name,
    c.phone_number,
    o.vendor_id,
    v.business_name,
    o.courier_id,
    cou.name,
    cr.phone_number,
    o.total_amount,
    o.status,
    o.delivery_address,
    o.payment_method,
    o.notes,
    o.created_at,
    o.estimated_delivery_time,
    COALESCE(
      json_agg(
        json_build_object(
          'product_id', oi.product_id,
          'product_name', p.name_ar,
          'quantity', oi.quantity,
          'unit_price', oi.unit_price,
          'total_price', oi.total_price,
          'image_url', pi.url,
          'blur_hash', pi.blur_hash
        )
      ) FILTER (WHERE oi.id IS NOT NULL),
      '[]'::json
    ) as items
  FROM orders o
  JOIN customers c ON o.customer_id = c.id
  JOIN users cu ON c.id = cu.id
  JOIN vendors v ON o.vendor_id = v.id
  LEFT JOIN couriers cr ON o.courier_id = cr.id
  LEFT JOIN users cou ON cr.id = cou.id
  LEFT JOIN order_items oi ON o.id = oi.order_id
  LEFT JOIN products p ON oi.product_id = p.id
  LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.sort_order = 0
  WHERE o.id = order_id_param
  GROUP BY o.id, cu.name, c.phone_number, v.business_name, cou.name, cr.phone_number;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to calculate vendor statistics
CREATE OR REPLACE FUNCTION get_vendor_statistics(vendor_id_param UUID)
RETURNS TABLE (
  total_products INTEGER,
  active_products INTEGER,
  total_orders INTEGER,
  completed_orders INTEGER,
  pending_orders INTEGER,
  total_revenue DECIMAL,
  average_order_value DECIMAL,
  current_rating DECIMAL
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    (SELECT COUNT(*)::INTEGER FROM products WHERE vendor_id = vendor_id_param),
    (SELECT COUNT(*)::INTEGER FROM products WHERE vendor_id = vendor_id_param AND is_active = true),
    (SELECT COUNT(*)::INTEGER FROM orders WHERE vendor_id = vendor_id_param),
    (SELECT COUNT(*)::INTEGER FROM orders WHERE vendor_id = vendor_id_param AND status = 'delivered'),
    (SELECT COUNT(*)::INTEGER FROM orders WHERE vendor_id = vendor_id_param AND status IN ('pending', 'confirmed', 'preparing')),
    (SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE vendor_id = vendor_id_param AND status = 'delivered'),
    (SELECT COALESCE(AVG(total_amount), 0) FROM orders WHERE vendor_id = vendor_id_param AND status = 'delivered'),
    (SELECT rating FROM vendors WHERE id = vendor_id_param);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update vendor rating based on completed orders
CREATE OR REPLACE FUNCTION update_vendor_rating(vendor_id_param UUID)
RETURNS VOID AS $$
DECLARE
  new_rating DECIMAL;
BEGIN
  -- This is a simplified rating calculation
  -- In a real system, you'd have a separate ratings table
  SELECT 
    CASE 
      WHEN COUNT(*) = 0 THEN 0
      ELSE LEAST(5.0, GREATEST(1.0, 4.0 + (COUNT(*) FILTER (WHERE status = 'delivered')::DECIMAL / GREATEST(COUNT(*), 1) - 0.8) * 5))
    END INTO new_rating
  FROM orders 
  WHERE vendor_id = vendor_id_param;
  
  UPDATE vendors 
  SET rating = new_rating, updated_at = NOW()
  WHERE id = vendor_id_param;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to assign courier to order
CREATE OR REPLACE FUNCTION assign_courier_to_order(
  order_id_param UUID,
  courier_id_param UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  order_exists BOOLEAN;
  courier_available BOOLEAN;
BEGIN
  -- Check if order exists and doesn't have a courier
  SELECT EXISTS(
    SELECT 1 FROM orders 
    WHERE id = order_id_param AND courier_id IS NULL AND status IN ('confirmed', 'ready_for_pickup')
  ) INTO order_exists;
  
  -- Check if courier is available
  SELECT is_available FROM couriers WHERE id = courier_id_param INTO courier_available;
  
  IF order_exists AND courier_available THEN
    UPDATE orders 
    SET courier_id = courier_id_param, 
        status = 'picked_up',
        updated_at = NOW()
    WHERE id = order_id_param;
    
    -- Log status change
    INSERT INTO order_status_history (order_id, status, changed_by, notes)
    VALUES (order_id_param, 'picked_up', courier_id_param, 'Courier assigned and order picked up');
    
    RETURN TRUE;
  ELSE
    RETURN FALSE;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to validate order before creation
CREATE OR REPLACE FUNCTION validate_order_items(items JSONB)
RETURNS TABLE (
  is_valid BOOLEAN,
  error_message TEXT,
  total_amount DECIMAL
) AS $$
DECLARE
  item JSONB;
  product_record RECORD;
  calculated_total DECIMAL := 0;
BEGIN
  -- Iterate through each item
  FOR item IN SELECT * FROM jsonb_array_elements(items)
  LOOP
    -- Get product details
    SELECT p.id, p.price, p.stock_quantity, p.is_active, v.is_verified
    INTO product_record
    FROM products p
    JOIN vendors v ON p.vendor_id = v.id
    WHERE p.id = (item->>'product_id')::UUID;
    
    -- Check if product exists
    IF product_record.id IS NULL THEN
      RETURN QUERY SELECT FALSE, 'Product not found: ' || (item->>'product_id'), 0::DECIMAL;
      RETURN;
    END IF;
    
    -- Check if product is active and vendor is verified
    IF NOT product_record.is_active OR NOT product_record.is_verified THEN
      RETURN QUERY SELECT FALSE, 'Product not available: ' || (item->>'product_id'), 0::DECIMAL;
      RETURN;
    END IF;
    
    -- Check stock availability
    IF product_record.stock_quantity < (item->>'quantity')::INTEGER THEN
      RETURN QUERY SELECT FALSE, 'Insufficient stock for product: ' || (item->>'product_id'), 0::DECIMAL;
      RETURN;
    END IF;
    
    -- Add to total
    calculated_total := calculated_total + (product_record.price * (item->>'quantity')::INTEGER);
  END LOOP;
  
  RETURN QUERY SELECT TRUE, 'Valid'::TEXT, calculated_total;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create order with items atomically
CREATE OR REPLACE FUNCTION create_order_with_items(
  customer_id_param UUID,
  vendor_id_param UUID,
  delivery_address_param JSONB,
  items_param JSONB,
  notes_param TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  new_order_id UUID;
  item JSONB;
  product_price DECIMAL;
  validation_result RECORD;
BEGIN
  -- Validate items first
  SELECT * FROM validate_order_items(items_param) INTO validation_result;
  
  IF NOT validation_result.is_valid THEN
    RAISE EXCEPTION 'Order validation failed: %', validation_result.error_message;
  END IF;
  
  -- Create the order
  INSERT INTO orders (customer_id, vendor_id, total_amount, delivery_address, notes)
  VALUES (customer_id_param, vendor_id_param, validation_result.total_amount, delivery_address_param, notes_param)
  RETURNING id INTO new_order_id;
  
  -- Insert order items
  FOR item IN SELECT * FROM jsonb_array_elements(items_param)
  LOOP
    SELECT price INTO product_price FROM products WHERE id = (item->>'product_id')::UUID;
    
    INSERT INTO order_items (order_id, product_id, quantity, unit_price, total_price)
    VALUES (
      new_order_id,
      (item->>'product_id')::UUID,
      (item->>'quantity')::INTEGER,
      product_price,
      product_price * (item->>'quantity')::INTEGER
    );
    
    -- Update product stock
    UPDATE products 
    SET stock_quantity = stock_quantity - (item->>'quantity')::INTEGER,
        updated_at = NOW()
    WHERE id = (item->>'product_id')::UUID;
  END LOOP;
  
  -- Log initial status
  INSERT INTO order_status_history (order_id, status, changed_by, notes)
  VALUES (new_order_id, 'pending', customer_id_param, 'Order created');
  
  RETURN new_order_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION get_available_couriers_near_location TO authenticated;
GRANT EXECUTE ON FUNCTION search_products TO authenticated;
GRANT EXECUTE ON FUNCTION get_order_details TO authenticated;
GRANT EXECUTE ON FUNCTION get_vendor_statistics TO authenticated;
GRANT EXECUTE ON FUNCTION update_vendor_rating TO authenticated;
GRANT EXECUTE ON FUNCTION assign_courier_to_order TO authenticated;
GRANT EXECUTE ON FUNCTION validate_order_items TO authenticated;
GRANT EXECUTE ON FUNCTION create_order_with_items TO authenticated;