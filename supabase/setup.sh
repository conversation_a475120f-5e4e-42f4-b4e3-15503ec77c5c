#!/bin/bash

# Supabase setup script for multi-vendor ecommerce platform
# This script initializes the local Supabase environment

set -e

echo "🚀 Setting up Supabase for Multi-Vendor Ecommerce Platform..."

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI is not installed. Please install it first:"
    echo "npm install -g supabase"
    exit 1
fi

# Check if Docker is running
if ! docker info &> /dev/null; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

echo "✅ Prerequisites check passed"

# Initialize Supabase if not already initialized
if [ ! -f "supabase/config.toml" ]; then
    echo "📦 Initializing Supabase project..."
    supabase init
else
    echo "✅ Supabase project already initialized"
fi

# Start Supabase services
echo "🔄 Starting Supabase services..."
supabase start

# Wait for services to be ready
echo "⏳ Waiting for services to be ready..."
sleep 10

# Apply migrations
echo "📊 Applying database migrations..."
supabase db reset --linked=false

# Load seed data
echo "🌱 Loading seed data..."
if [ -f "supabase/seed.sql" ]; then
    supabase db seed
else
    echo "⚠️  No seed.sql file found, skipping seed data"
fi

# Get the local URLs and keys
echo ""
echo "🎉 Setup complete! Your local Supabase is ready."
echo ""
echo "📋 Connection Details:"
echo "API URL: http://localhost:54321"
echo "Studio URL: http://localhost:54323"
echo "Inbucket URL: http://localhost:54324"
echo ""

# Extract and display the anon key
ANON_KEY=$(supabase status | grep "anon key" | awk '{print $3}')
SERVICE_ROLE_KEY=$(supabase status | grep "service_role key" | awk '{print $3}')

echo "🔑 API Keys:"
echo "Anon Key: $ANON_KEY"
echo "Service Role Key: $SERVICE_ROLE_KEY"
echo ""

# Create .env.local file if it doesn't exist
if [ ! -f "../.env.local" ]; then
    echo "📝 Creating .env.local file..."
    cat > ../.env.local << EOF
# Supabase Configuration
SUPABASE_URL=http://localhost:54321
SUPABASE_ANON_KEY=$ANON_KEY
SUPABASE_SERVICE_ROLE_KEY=$SERVICE_ROLE_KEY

# Database Configuration
DATABASE_URL=postgresql://postgres:postgres@localhost:54322/postgres
EOF
    echo "✅ .env.local file created"
else
    echo "⚠️  .env.local file already exists, please update it manually with the keys above"
fi

echo ""
echo "🔧 Next Steps:"
echo "1. Open Supabase Studio at http://localhost:54323"
echo "2. Update your Flutter apps with the connection details"
echo "3. Test the API endpoints using the provided keys"
echo ""
echo "📚 Useful Commands:"
echo "- supabase status: Check service status"
echo "- supabase stop: Stop all services"
echo "- supabase db reset: Reset database with fresh migrations"
echo "- supabase logs: View service logs"
echo ""
echo "Happy coding! 🎯"