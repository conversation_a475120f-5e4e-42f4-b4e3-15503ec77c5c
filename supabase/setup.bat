@echo off
REM Supabase setup script for multi-vendor ecommerce platform (Windows)
REM This script initializes the local Supabase environment

echo 🚀 Setting up Supabase for Multi-Vendor Ecommerce Platform...

REM Check if Supabase CLI is installed
supabase --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Supabase CLI is not installed. Please install it first:
    echo npm install -g supabase
    exit /b 1
)

REM Check if Docker is running
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not running. Please start Docker first.
    exit /b 1
)

echo ✅ Prerequisites check passed

REM Initialize Supabase if not already initialized
if not exist "supabase\config.toml" (
    echo 📦 Initializing Supabase project...
    supabase init
) else (
    echo ✅ Supabase project already initialized
)

REM Start Supabase services
echo 🔄 Starting Supabase services...
supabase start

REM Wait for services to be ready
echo ⏳ Waiting for services to be ready...
timeout /t 10 /nobreak >nul

REM Apply migrations
echo 📊 Applying database migrations...
supabase db reset --linked=false

REM Load seed data
echo 🌱 Loading seed data...
if exist "supabase\seed.sql" (
    supabase db seed
) else (
    echo ⚠️  No seed.sql file found, skipping seed data
)

echo.
echo 🎉 Setup complete! Your local Supabase is ready.
echo.
echo 📋 Connection Details:
echo API URL: http://localhost:54321
echo Studio URL: http://localhost:54323
echo Inbucket URL: http://localhost:54324
echo.

REM Get status and display keys
echo 🔑 Getting API Keys...
supabase status

echo.
echo 🔧 Next Steps:
echo 1. Open Supabase Studio at http://localhost:54323
echo 2. Update your Flutter apps with the connection details
echo 3. Test the API endpoints using the provided keys
echo.
echo 📚 Useful Commands:
echo - supabase status: Check service status
echo - supabase stop: Stop all services
echo - supabase db reset: Reset database with fresh migrations
echo - supabase logs: View service logs
echo.
echo Happy coding! 🎯

pause