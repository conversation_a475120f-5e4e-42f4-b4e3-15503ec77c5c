-- Seed data for multi-vendor ecommerce platform testing
-- This file contains test data for development and testing purposes

-- Insert test users
INSERT INTO users (id, email, name, user_type) VALUES
  ('550e8400-e29b-41d4-a716-446655440001', '<EMAIL>', 'أحمد محمد', 'customer'),
  ('550e8400-e29b-41d4-a716-446655440002', '<EMAIL>', 'فاطمة علي', 'customer'),
  ('550e8400-e29b-41d4-a716-446655440003', '<EMAIL>', 'محمد حسن', 'vendor'),
  ('550e8400-e29b-41d4-a716-446655440004', '<EMAIL>', 'سارة أحمد', 'vendor'),
  ('550e8400-e29b-41d4-a716-446655440005', '<EMAIL>', 'عبدالله خالد', 'courier'),
  ('550e8400-e29b-41d4-a716-446655440006', '<EMAIL>', 'يوسف عمر', 'courier');

-- Insert test customers (will be created automatically by trigger, but we can update them)
UPDATE customers SET 
  phone_number = '+966501234567',
  favorite_vendors = ARRAY['550e8400-e29b-41d4-a716-446655440003']
WHERE id = '550e8400-e29b-41d4-a716-446655440001';

UPDATE customers SET 
  phone_number = '+966507654321',
  favorite_vendors = ARRAY['550e8400-e29b-41d4-a716-446655440004']
WHERE id = '550e8400-e29b-41d4-a716-446655440002';

-- Insert test vendors
UPDATE vendors SET 
  business_name = 'متجر الإلكترونيات الذكية',
  category = 'electronics',
  description = 'متجر متخصص في بيع الأجهزة الإلكترونية والهواتف الذكية',
  business_address = '{"street": "شارع الملك فهد", "city": "الرياض", "district": "العليا", "building": "123"}',
  is_verified = true,
  rating = 4.5
WHERE id = '550e8400-e29b-41d4-a716-446655440003';

UPDATE vendors SET 
  business_name = 'بوتيك الأزياء العصرية',
  category = 'clothing',
  description = 'متجر أزياء نسائية عصرية وأنيقة',
  business_address = '{"street": "شارع التحلية", "city": "جدة", "district": "الحمراء", "building": "456"}',
  is_verified = true,
  rating = 4.8
WHERE id = '550e8400-e29b-41d4-a716-446655440004';

-- Insert test couriers
UPDATE couriers SET 
  phone_number = '+966551234567',
  vehicle_type = 'motorcycle',
  is_available = true,
  current_location = ST_Point(46.6753, 24.7136) -- Riyadh coordinates
WHERE id = '550e8400-e29b-41d4-a716-446655440005';

UPDATE couriers SET 
  phone_number = '+966557654321',
  vehicle_type = 'car',
  is_available = true,
  current_location = ST_Point(39.2082, 21.4858) -- Jeddah coordinates
WHERE id = '550e8400-e29b-41d4-a716-446655440006';

-- Insert test addresses
INSERT INTO addresses (id, customer_id, label, street_address, city, district, building_number, floor_number, apartment_number, phone_number, is_default, location) VALUES
  ('650e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', 'المنزل', 'شارع الأمير محمد بن عبدالعزيز', 'الرياض', 'الملز', '789', '2', '5', '+966501234567', true, ST_Point(46.6753, 24.7136)),
  ('650e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440001', 'العمل', 'شارع العليا العام', 'الرياض', 'العليا', '321', '1', '10', '+966501234567', false, ST_Point(46.6853, 24.7236)),
  ('650e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440002', 'المنزل', 'شارع الأمير سلطان', 'جدة', 'الروضة', '654', '3', '8', '+966507654321', true, ST_Point(39.2082, 21.4858));

-- Insert test products
INSERT INTO products (id, vendor_id, name_ar, description_ar, price, stock_quantity, category, weight_grams, dimensions_cm) VALUES
  ('750e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440003', 'هاتف ذكي سامسونج جالاكسي', 'هاتف ذكي بمواصفات عالية وكاميرا متطورة', 2500.00, 15, 'electronics', 200, '{"width": 7, "height": 15, "depth": 1}'),
  ('750e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440003', 'سماعات لاسلكية', 'سماعات بلوتوث عالية الجودة', 350.00, 25, 'electronics', 50, '{"width": 5, "height": 5, "depth": 3}'),
  ('750e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440004', 'فستان صيفي أنيق', 'فستان صيفي بألوان زاهية ومقاسات متنوعة', 180.00, 30, 'clothing', 300, '{"width": 30, "height": 40, "depth": 2}'),
  ('750e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440004', 'حقيبة يد جلدية', 'حقيبة يد نسائية من الجلد الطبيعي', 420.00, 12, 'clothing', 800, '{"width": 25, "height": 20, "depth": 10}');

-- Insert test product images
INSERT INTO product_images (id, product_id, url, blur_hash, width, height, sort_order) VALUES
  ('850e8400-e29b-41d4-a716-446655440001', '750e8400-e29b-41d4-a716-446655440001', 'https://example.com/images/phone1.jpg', 'LGF5]+Yk^6#M@-5c,1J5@[or[Q6.', 800, 600, 0),
  ('850e8400-e29b-41d4-a716-446655440002', '750e8400-e29b-41d4-a716-446655440001', 'https://example.com/images/phone1_2.jpg', 'LGF5]+Yk^6#M@-5c,1J5@[or[Q6.', 800, 600, 1),
  ('850e8400-e29b-41d4-a716-446655440003', '750e8400-e29b-41d4-a716-446655440002', 'https://example.com/images/headphones1.jpg', 'LKO2?U%2Tw=w]~RBVZRi};RPxuwH', 800, 600, 0),
  ('850e8400-e29b-41d4-a716-446655440004', '750e8400-e29b-41d4-a716-446655440003', 'https://example.com/images/dress1.jpg', 'LBAdAqof00WCqZj[PDay0.WB}pof', 800, 600, 0),
  ('850e8400-e29b-41d4-a716-446655440005', '750e8400-e29b-41d4-a716-446655440004', 'https://example.com/images/bag1.jpg', 'LTI#+9xuRjWB_4t7t7R**0o#DgR*', 800, 600, 0);

-- Insert test orders
INSERT INTO orders (id, customer_id, vendor_id, total_amount, status, delivery_address, notes, estimated_delivery_time) VALUES
  ('950e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440003', 2850.00, 'confirmed', 
   '{"street": "شارع الأمير محمد بن عبدالعزيز", "city": "الرياض", "district": "الملز", "building": "789", "floor": "2", "apartment": "5", "phone": "+966501234567"}',
   'يرجى الاتصال قبل الوصول', NOW() + INTERVAL '2 days'),
  ('950e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440004', 600.00, 'preparing',
   '{"street": "شارع الأمير سلطان", "city": "جدة", "district": "الروضة", "building": "654", "floor": "3", "apartment": "8", "phone": "+966507654321"}',
   'التسليم في المساء', NOW() + INTERVAL '1 day');

-- Insert test order items
INSERT INTO order_items (id, order_id, product_id, quantity, unit_price, total_price) VALUES
  ('a50e8400-e29b-41d4-a716-446655440001', '950e8400-e29b-41d4-a716-446655440001', '750e8400-e29b-41d4-a716-446655440001', 1, 2500.00, 2500.00),
  ('a50e8400-e29b-41d4-a716-446655440002', '950e8400-e29b-41d4-a716-446655440001', '750e8400-e29b-41d4-a716-446655440002', 1, 350.00, 350.00),
  ('a50e8400-e29b-41d4-a716-446655440003', '950e8400-e29b-41d4-a716-446655440002', '750e8400-e29b-41d4-a716-446655440003', 1, 180.00, 180.00),
  ('a50e8400-e29b-41d4-a716-446655440004', '950e8400-e29b-41d4-a716-446655440002', '750e8400-e29b-41d4-a716-446655440004', 1, 420.00, 420.00);

-- Insert initial order status history
INSERT INTO order_status_history (order_id, status, changed_by, notes) VALUES
  ('950e8400-e29b-41d4-a716-446655440001', 'pending', '550e8400-e29b-41d4-a716-446655440001', 'Order created'),
  ('950e8400-e29b-41d4-a716-446655440001', 'confirmed', '550e8400-e29b-41d4-a716-446655440003', 'Order confirmed by vendor'),
  ('950e8400-e29b-41d4-a716-446655440002', 'pending', '550e8400-e29b-41d4-a716-446655440002', 'Order created'),
  ('950e8400-e29b-41d4-a716-446655440002', 'preparing', '550e8400-e29b-41d4-a716-446655440004', 'Order being prepared');

-- Create some additional test data for better testing coverage

-- More products for testing pagination and search
INSERT INTO products (id, vendor_id, name_ar, description_ar, price, stock_quantity, category, weight_grams) VALUES
  ('750e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440003', 'لابتوب ديل', 'لابتوب للألعاب بمعالج قوي', 4500.00, 8, 'electronics', 2500),
  ('750e8400-e29b-41d4-a716-446655440006', '550e8400-e29b-41d4-a716-446655440003', 'ماوس لاسلكي', 'ماوس لاسلكي للألعاب', 120.00, 50, 'electronics', 100),
  ('750e8400-e29b-41d4-a716-446655440007', '550e8400-e29b-41d4-a716-446655440004', 'بلوزة قطنية', 'بلوزة قطنية مريحة للاستخدام اليومي', 85.00, 40, 'clothing', 200),
  ('750e8400-e29b-41d4-a716-446655440008', '550e8400-e29b-41d4-a716-446655440004', 'حذاء رياضي', 'حذاء رياضي مريح للمشي والجري', 320.00, 20, 'clothing', 600);

-- Add images for the new products
INSERT INTO product_images (id, product_id, url, blur_hash, width, height, sort_order) VALUES
  ('850e8400-e29b-41d4-a716-446655440006', '750e8400-e29b-41d4-a716-446655440005', 'https://example.com/images/laptop1.jpg', 'L6Pj0^jE.AyE_3t7t7R**0o#DgR*', 800, 600, 0),
  ('850e8400-e29b-41d4-a716-446655440007', '750e8400-e29b-41d4-a716-446655440006', 'https://example.com/images/mouse1.jpg', 'LKO2?U%2Tw=w]~RBVZRi};RPxuwH', 800, 600, 0),
  ('850e8400-e29b-41d4-a716-446655440008', '750e8400-e29b-41d4-a716-446655440007', 'https://example.com/images/blouse1.jpg', 'LBAdAqof00WCqZj[PDay0.WB}pof', 800, 600, 0),
  ('850e8400-e29b-41d4-a716-446655440009', '750e8400-e29b-41d4-a716-446655440008', 'https://example.com/images/shoes1.jpg', 'LTI#+9xuRjWB_4t7t7R**0o#DgR*', 800, 600, 0);

-- Update stock quantities after order items (simulating inventory management)
UPDATE products SET stock_quantity = stock_quantity - 1 WHERE id = '750e8400-e29b-41d4-a716-446655440001';
UPDATE products SET stock_quantity = stock_quantity - 1 WHERE id = '750e8400-e29b-41d4-a716-446655440002';
UPDATE products SET stock_quantity = stock_quantity - 1 WHERE id = '750e8400-e29b-41d4-a716-446655440003';
UPDATE products SET stock_quantity = stock_quantity - 1 WHERE id = '750e8400-e29b-41d4-a716-446655440004';