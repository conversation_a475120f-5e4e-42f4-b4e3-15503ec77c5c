# Supabase Backend Setup

This directory contains the database schema, migrations, and configuration for the multi-vendor ecommerce platform backend using Supabase.

## Prerequisites

1. Install Supabase CLI:
   ```bash
   npm install -g supabase
   ```

2. Install Docker (required for local development)

## Local Development Setup

1. Initialize Supabase in your project:
   ```bash
   supabase init
   ```

2. Start the local Supabase stack:
   ```bash
   supabase start
   ```

3. Apply migrations:
   ```bash
   supabase db reset
   ```

4. Load seed data:
   ```bash
   supabase db seed
   ```

## Database Schema Overview

### Core Tables

#### Users and Profiles
- `users` - Base user table for all user types
- `customers` - Customer-specific profile data
- `vendors` - Vendor-specific profile data  
- `couriers` - Courier-specific profile data

#### Products and Inventory
- `products` - Product catalog with Arabic names and descriptions
- `product_images` - Product images with BlurHash support

#### Orders and Transactions
- `orders` - Order management with cash-on-delivery support
- `order_items` - Individual items within orders
- `order_status_history` - Audit trail for order status changes

#### Location and Delivery
- `addresses` - Customer delivery addresses with geolocation
- Spatial indexes for location-based courier assignment

### Security Features

#### Row Level Security (RLS)
All tables have RLS enabled with policies that ensure:
- Users can only access their own data
- Customers can view active products from verified vendors
- Vendors can manage their own products and orders
- Couriers can access assigned delivery information

#### Automatic Triggers
- User profile creation based on user type
- Order total calculation when items change
- Order status change logging
- Updated timestamp maintenance

## Environment Variables

Create a `.env.local` file in your project root with:

```env
SUPABASE_URL=http://localhost:54321
SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

## Production Deployment

1. Create a new Supabase project at https://supabase.com

2. Link your local project:
   ```bash
   supabase link --project-ref your-project-ref
   ```

3. Push migrations to production:
   ```bash
   supabase db push
   ```

4. Update your Flutter apps with production Supabase credentials

## Testing Data

The seed file includes test data for:
- 2 customers with Arabic names
- 2 vendors (electronics and clothing)
- 2 couriers with different vehicle types
- Sample products with Arabic descriptions
- Test orders in various statuses
- Customer addresses with geolocation

## API Usage Examples

### Authentication
Users authenticate via magic link only. The system automatically creates the appropriate profile (customer, vendor, or courier) based on the user type specified during registration.

### Product Queries
```sql
-- Get all active products
SELECT * FROM products WHERE is_active = true;

-- Get products by category
SELECT * FROM products WHERE category = 'electronics' AND is_active = true;

-- Get vendor's products
SELECT * FROM products WHERE vendor_id = 'vendor-uuid';
```

### Order Management
```sql
-- Get customer orders
SELECT * FROM orders WHERE customer_id = 'customer-uuid';

-- Get vendor orders
SELECT * FROM orders WHERE vendor_id = 'vendor-uuid';

-- Get courier deliveries
SELECT * FROM orders WHERE courier_id = 'courier-uuid';
```

## Monitoring and Maintenance

### Performance Monitoring
- Monitor query performance using Supabase dashboard
- Check index usage for location-based queries
- Monitor RLS policy performance

### Data Integrity
- Regular backups are handled by Supabase
- Order totals are automatically maintained by triggers
- Status changes are logged for audit purposes

### Security Auditing
- Review RLS policies regularly
- Monitor authentication logs
- Check for unusual data access patterns

## Troubleshooting

### Common Issues

1. **Migration Errors**: Ensure all dependencies are installed and Docker is running
2. **RLS Policy Issues**: Check that auth.uid() matches the user making the request
3. **Trigger Failures**: Verify that all referenced tables and columns exist
4. **Seed Data Issues**: Ensure UUIDs are unique and foreign key constraints are satisfied

### Useful Commands

```bash
# Reset database to clean state
supabase db reset

# Generate new migration
supabase migration new migration_name

# Check migration status
supabase migration list

# View logs
supabase logs

# Stop local stack
supabase stop
```

## Support

For issues related to:
- Supabase setup: Check the [official documentation](https://supabase.com/docs)
- Schema design: Review the design document in the specs folder
- Flutter integration: See the API client package documentation