-- Test queries to verify the database schema and functionality
-- Run these queries to ensure everything is working correctly

-- Test 1: Verify all tables exist
SELECT 
  schemaname,
  tablename,
  tableowner
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename IN (
    'users', 'customers', 'vendors', 'couriers', 
    'addresses', 'products', 'product_images', 
    'orders', 'order_items', 'order_status_history'
  )
ORDER BY tablename;

-- Test 2: Verify enums are created
SELECT 
  t.typname as enum_name,
  array_agg(e.enumlabel ORDER BY e.enumsortorder) as enum_values
FROM pg_type t 
JOIN pg_enum e ON t.oid = e.enumtypid  
WHERE t.typname IN (
  'user_type_enum', 'vendor_category_enum', 
  'product_category_enum', 'order_status_enum', 
  'payment_method_enum'
)
GROUP BY t.typname
ORDER BY t.typname;

-- Test 3: Verify indexes exist
SELECT 
  indexname,
  tablename,
  indexdef
FROM pg_indexes 
WHERE schemaname = 'public'
  AND indexname LIKE 'idx_%'
ORDER BY tablename, indexname;

-- Test 4: Verify RLS is enabled
SELECT 
  schemaname,
  tablename,
  rowsecurity
FROM pg_tables 
WHERE schemaname = 'public' 
  AND tablename IN (
    'users', 'customers', 'vendors', 'couriers', 
    'addresses', 'products', 'product_images', 
    'orders', 'order_items', 'order_status_history'
  )
ORDER BY tablename;

-- Test 5: Count RLS policies
SELECT 
  schemaname,
  tablename,
  COUNT(*) as policy_count
FROM pg_policies 
WHERE schemaname = 'public'
GROUP BY schemaname, tablename
ORDER BY tablename;

-- Test 6: Verify functions exist
SELECT 
  routine_name,
  routine_type,
  data_type
FROM information_schema.routines 
WHERE routine_schema = 'public'
  AND routine_name IN (
    'get_available_couriers_near_location',
    'search_products',
    'get_order_details',
    'get_vendor_statistics',
    'create_order_with_items'
  )
ORDER BY routine_name;

-- Test 7: Verify triggers exist
SELECT 
  trigger_name,
  event_manipulation,
  event_object_table,
  trigger_schema
FROM information_schema.triggers 
WHERE trigger_schema = 'public'
ORDER BY event_object_table, trigger_name;

-- Test 8: Test seed data - count records in each table
SELECT 'users' as table_name, COUNT(*) as record_count FROM users
UNION ALL
SELECT 'customers', COUNT(*) FROM customers
UNION ALL
SELECT 'vendors', COUNT(*) FROM vendors
UNION ALL
SELECT 'couriers', COUNT(*) FROM couriers
UNION ALL
SELECT 'addresses', COUNT(*) FROM addresses
UNION ALL
SELECT 'products', COUNT(*) FROM products
UNION ALL
SELECT 'product_images', COUNT(*) FROM product_images
UNION ALL
SELECT 'orders', COUNT(*) FROM orders
UNION ALL
SELECT 'order_items', COUNT(*) FROM order_items
UNION ALL
SELECT 'order_status_history', COUNT(*) FROM order_status_history
ORDER BY table_name;

-- Test 9: Test search function
SELECT * FROM search_products('هاتف', NULL, NULL, 0, NULL, 5, 0);

-- Test 10: Test vendor statistics function
SELECT * FROM get_vendor_statistics('550e8400-e29b-41d4-a716-446655440003');

-- Test 11: Test order details function
SELECT * FROM get_order_details('950e8400-e29b-41d4-a716-446655440001');

-- Test 12: Test courier location function
SELECT * FROM get_available_couriers_near_location(24.7136, 46.6753, 20);

-- Test 13: Verify foreign key constraints
SELECT
  tc.table_name,
  kcu.column_name,
  ccu.table_name AS foreign_table_name,
  ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
  ON tc.constraint_name = kcu.constraint_name
  AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
  ON ccu.constraint_name = tc.constraint_name
  AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
  AND tc.table_schema = 'public'
ORDER BY tc.table_name, kcu.column_name;

-- Test 14: Verify check constraints
SELECT
  tc.table_name,
  tc.constraint_name,
  cc.check_clause
FROM information_schema.table_constraints tc
JOIN information_schema.check_constraints cc
  ON tc.constraint_name = cc.constraint_name
WHERE tc.constraint_type = 'CHECK'
  AND tc.table_schema = 'public'
ORDER BY tc.table_name;

-- Test 15: Test order total calculation trigger
-- This should automatically update the order total when items are modified
BEGIN;
  -- Insert a test order
  INSERT INTO orders (id, customer_id, vendor_id, total_amount, delivery_address)
  VALUES ('test-order-id', '550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440003', 0, '{}');
  
  -- Insert order items (should trigger total calculation)
  INSERT INTO order_items (order_id, product_id, quantity, unit_price, total_price)
  VALUES 
    ('test-order-id', '750e8400-e29b-41d4-a716-446655440001', 1, 100.00, 100.00),
    ('test-order-id', '750e8400-e29b-41d4-a716-446655440002', 2, 50.00, 100.00);
  
  -- Check if total was updated correctly
  SELECT id, total_amount FROM orders WHERE id = 'test-order-id';
  
  -- Clean up
ROLLBACK;

-- Test 16: Verify spatial indexes work
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM get_available_couriers_near_location(24.7136, 46.6753, 10);

-- Test 17: Test Arabic text handling
SELECT 
  name_ar,
  description_ar,
  LENGTH(name_ar) as name_length,
  LENGTH(description_ar) as desc_length
FROM products 
WHERE name_ar IS NOT NULL
LIMIT 3;

-- Test 18: Verify updated_at triggers work
BEGIN;
  UPDATE products SET price = price + 1 WHERE id = '750e8400-e29b-41d4-a716-446655440001';
  SELECT id, price, updated_at FROM products WHERE id = '750e8400-e29b-41d4-a716-446655440001';
ROLLBACK;

-- Test 19: Test order validation function
SELECT * FROM validate_order_items('[
  {"product_id": "750e8400-e29b-41d4-a716-446655440001", "quantity": 1},
  {"product_id": "750e8400-e29b-41d4-a716-446655440002", "quantity": 2}
]'::jsonb);

-- Test 20: Performance test - complex query with joins
EXPLAIN (ANALYZE, BUFFERS)
SELECT 
  p.name_ar,
  v.business_name,
  COUNT(oi.id) as times_ordered,
  AVG(oi.unit_price) as avg_price
FROM products p
JOIN vendors v ON p.vendor_id = v.id
LEFT JOIN order_items oi ON p.id = oi.product_id
WHERE p.is_active = true
GROUP BY p.id, p.name_ar, v.business_name
HAVING COUNT(oi.id) > 0
ORDER BY times_ordered DESC
LIMIT 10;