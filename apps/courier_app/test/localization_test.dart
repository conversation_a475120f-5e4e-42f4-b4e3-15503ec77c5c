import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:courier_app/l10n/generated/app_localizations.dart';

void main() {
  group('Courier App Arabic Localization Tests', () {
    testWidgets('should display courier-specific Arabic text correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [Locale('ar')],
          locale: const Locale('ar'),
          home: Scaffold(
            appBar: AppBar(
              title: Builder(
                builder: (context) =>
                    Text(AppLocalizations.of(context)!.appTitle),
              ),
            ),
            body: Builder(
              builder: (context) => Column(
                children: [
                  Text(AppLocalizations.of(context)!.deliveries),
                  Text(AppLocalizations.of(context)!.availableDeliveries),
                  Text(AppLocalizations.of(context)!.myDeliveries),
                  Text(AppLocalizations.of(context)!.vehicleType),
                ],
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify courier-specific Arabic text
      expect(find.text('تطبيق المندوبين'), findsOneWidget);
      expect(find.text('التوصيلات'), findsOneWidget);
      expect(find.text('التوصيلات المتاحة'), findsOneWidget);
      expect(find.text('توصيلاتي'), findsOneWidget);
      expect(find.text('نوع المركبة'), findsOneWidget);
    });

    testWidgets('should handle delivery actions in Arabic',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [Locale('ar')],
          locale: const Locale('ar'),
          home: Scaffold(
            body: Builder(
              builder: (context) => Column(
                children: [
                  ElevatedButton(
                    onPressed: () {},
                    child: Text(AppLocalizations.of(context)!.acceptDelivery),
                  ),
                  TextButton(
                    onPressed: () {},
                    child: Text(AppLocalizations.of(context)!.rejectDelivery),
                  ),
                  ElevatedButton(
                    onPressed: () {},
                    child: Text(AppLocalizations.of(context)!.startDelivery),
                  ),
                  ElevatedButton(
                    onPressed: () {},
                    child: Text(AppLocalizations.of(context)!.completeDelivery),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify delivery action Arabic text
      expect(find.text('قبول التوصيل'), findsOneWidget);
      expect(find.text('رفض التوصيل'), findsOneWidget);
      expect(find.text('بدء التوصيل'), findsOneWidget);
      expect(find.text('إتمام التوصيل'), findsOneWidget);
    });

    testWidgets('should display delivery information in Arabic',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [Locale('ar')],
          locale: const Locale('ar'),
          home: Scaffold(
            body: Builder(
              builder: (context) => Column(
                children: [
                  Text(AppLocalizations.of(context)!.customerName),
                  Text(AppLocalizations.of(context)!.customerPhone),
                  Text(AppLocalizations.of(context)!.deliveryAddress),
                  Text(AppLocalizations.of(context)!.orderTotal),
                  Text(AppLocalizations.of(context)!.cashCollected),
                ],
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify delivery information Arabic text
      expect(find.text('اسم العميل'), findsOneWidget);
      expect(find.text('هاتف العميل'), findsOneWidget);
      expect(find.text('عنوان التوصيل'), findsOneWidget);
      expect(find.text('إجمالي الطلب'), findsOneWidget);
      expect(find.text('تم تحصيل النقد'), findsOneWidget);
    });

    testWidgets('should display status information in Arabic',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [Locale('ar')],
          locale: const Locale('ar'),
          home: Scaffold(
            body: Builder(
              builder: (context) => Column(
                children: [
                  Text(AppLocalizations.of(context)!.available),
                  Text(AppLocalizations.of(context)!.unavailable),
                  Text(AppLocalizations.of(context)!.inTransit),
                  Text(AppLocalizations.of(context)!.delivered),
                ],
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify status Arabic text
      expect(find.text('متاح'), findsOneWidget);
      expect(find.text('غير متاح'), findsOneWidget);
      expect(find.text('في الطريق'), findsOneWidget);
      expect(find.text('تم التوصيل'), findsOneWidget);
    });
  });
}
