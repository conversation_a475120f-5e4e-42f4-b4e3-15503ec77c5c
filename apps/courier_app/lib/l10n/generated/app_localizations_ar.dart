// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'تطبيق المندوبين';

  @override
  String get login => 'تسجيل الدخول';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get loading => 'جاري التحميل...';

  @override
  String get error => 'خطأ';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get deliveries => 'التوصيلات';

  @override
  String get availableDeliveries => 'التوصيلات المتاحة';

  @override
  String get myDeliveries => 'توصيلاتي';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get acceptDelivery => 'قبول التوصيل';

  @override
  String get rejectDelivery => 'رفض التوصيل';

  @override
  String get startDelivery => 'بدء التوصيل';

  @override
  String get completeDelivery => 'إتمام التوصيل';

  @override
  String get customerName => 'اسم العميل';

  @override
  String get customerPhone => 'هاتف العميل';

  @override
  String get deliveryAddress => 'عنوان التوصيل';

  @override
  String get orderTotal => 'إجمالي الطلب';

  @override
  String get cashCollected => 'تم تحصيل النقد';

  @override
  String get navigate => 'التنقل';

  @override
  String get callCustomer => 'اتصال بالعميل';

  @override
  String get vehicleType => 'نوع المركبة';

  @override
  String get phoneNumber => 'رقم الهاتف';

  @override
  String get available => 'متاح';

  @override
  String get unavailable => 'غير متاح';

  @override
  String get inTransit => 'في الطريق';

  @override
  String get delivered => 'تم التوصيل';

  @override
  String get save => 'حفظ';

  @override
  String get cancel => 'إلغاء';

  @override
  String get confirm => 'تأكيد';

  @override
  String get networkError => 'خطأ في الاتصال بالشبكة';

  @override
  String get authError => 'خطأ في المصادقة';

  @override
  String get validationError => 'خطأ في التحقق من البيانات';
}
