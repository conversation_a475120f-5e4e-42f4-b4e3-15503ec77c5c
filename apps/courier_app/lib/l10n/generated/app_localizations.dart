import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'generated/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
      : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[Locale('ar')];

  /// The title of the courier application
  ///
  /// In ar, this message translates to:
  /// **'تطبيق المندوبين'**
  String get appTitle;

  /// Login button text
  ///
  /// In ar, this message translates to:
  /// **'تسجيل الدخول'**
  String get login;

  /// Email field label
  ///
  /// In ar, this message translates to:
  /// **'البريد الإلكتروني'**
  String get email;

  /// Loading indicator text
  ///
  /// In ar, this message translates to:
  /// **'جاري التحميل...'**
  String get loading;

  /// Generic error text
  ///
  /// In ar, this message translates to:
  /// **'خطأ'**
  String get error;

  /// Retry button text
  ///
  /// In ar, this message translates to:
  /// **'إعادة المحاولة'**
  String get retry;

  /// Deliveries section
  ///
  /// In ar, this message translates to:
  /// **'التوصيلات'**
  String get deliveries;

  /// Available deliveries section
  ///
  /// In ar, this message translates to:
  /// **'التوصيلات المتاحة'**
  String get availableDeliveries;

  /// My deliveries section
  ///
  /// In ar, this message translates to:
  /// **'توصيلاتي'**
  String get myDeliveries;

  /// Courier profile
  ///
  /// In ar, this message translates to:
  /// **'الملف الشخصي'**
  String get profile;

  /// Accept delivery button
  ///
  /// In ar, this message translates to:
  /// **'قبول التوصيل'**
  String get acceptDelivery;

  /// Reject delivery button
  ///
  /// In ar, this message translates to:
  /// **'رفض التوصيل'**
  String get rejectDelivery;

  /// Start delivery button
  ///
  /// In ar, this message translates to:
  /// **'بدء التوصيل'**
  String get startDelivery;

  /// Complete delivery button
  ///
  /// In ar, this message translates to:
  /// **'إتمام التوصيل'**
  String get completeDelivery;

  /// Customer name field
  ///
  /// In ar, this message translates to:
  /// **'اسم العميل'**
  String get customerName;

  /// Customer phone field
  ///
  /// In ar, this message translates to:
  /// **'هاتف العميل'**
  String get customerPhone;

  /// Delivery address
  ///
  /// In ar, this message translates to:
  /// **'عنوان التوصيل'**
  String get deliveryAddress;

  /// Order total amount
  ///
  /// In ar, this message translates to:
  /// **'إجمالي الطلب'**
  String get orderTotal;

  /// Cash collected confirmation
  ///
  /// In ar, this message translates to:
  /// **'تم تحصيل النقد'**
  String get cashCollected;

  /// Navigate button
  ///
  /// In ar, this message translates to:
  /// **'التنقل'**
  String get navigate;

  /// Call customer button
  ///
  /// In ar, this message translates to:
  /// **'اتصال بالعميل'**
  String get callCustomer;

  /// Vehicle type field
  ///
  /// In ar, this message translates to:
  /// **'نوع المركبة'**
  String get vehicleType;

  /// Phone number field
  ///
  /// In ar, this message translates to:
  /// **'رقم الهاتف'**
  String get phoneNumber;

  /// Available status
  ///
  /// In ar, this message translates to:
  /// **'متاح'**
  String get available;

  /// Unavailable status
  ///
  /// In ar, this message translates to:
  /// **'غير متاح'**
  String get unavailable;

  /// In transit status
  ///
  /// In ar, this message translates to:
  /// **'في الطريق'**
  String get inTransit;

  /// Delivered status
  ///
  /// In ar, this message translates to:
  /// **'تم التوصيل'**
  String get delivered;

  /// Save button
  ///
  /// In ar, this message translates to:
  /// **'حفظ'**
  String get save;

  /// Cancel button
  ///
  /// In ar, this message translates to:
  /// **'إلغاء'**
  String get cancel;

  /// Confirm button
  ///
  /// In ar, this message translates to:
  /// **'تأكيد'**
  String get confirm;

  /// Network connection error message
  ///
  /// In ar, this message translates to:
  /// **'خطأ في الاتصال بالشبكة'**
  String get networkError;

  /// Authentication error message
  ///
  /// In ar, this message translates to:
  /// **'خطأ في المصادقة'**
  String get authError;

  /// Validation error message
  ///
  /// In ar, this message translates to:
  /// **'خطأ في التحقق من البيانات'**
  String get validationError;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['ar'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar':
      return AppLocalizationsAr();
  }

  throw FlutterError(
      'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
      'an issue with the localizations generation tool. Please file an issue '
      'on GitHub with a reproducible sample app and the gen-l10n configuration '
      'that was used.');
}
