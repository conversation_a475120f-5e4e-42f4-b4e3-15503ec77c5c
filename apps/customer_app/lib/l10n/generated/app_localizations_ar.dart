// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'تطبيق العملاء';

  @override
  String get login => 'تسجيل الدخول';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get loading => 'جاري التحميل...';

  @override
  String get error => 'خطأ';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get products => 'المنتجات';

  @override
  String get search => 'البحث';

  @override
  String get cart => 'السلة';

  @override
  String get orders => 'الطلبات';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get addToCart => 'إضافة للسلة';

  @override
  String get checkout => 'إتمام الطلب';

  @override
  String get orderHistory => 'تاريخ الطلبات';

  @override
  String get trackOrder => 'تتبع الطلب';

  @override
  String get cashOnDelivery => 'الدفع عند الاستلام';

  @override
  String get deliveryAddress => 'عنوان التوصيل';

  @override
  String get total => 'المجموع';

  @override
  String get cancel => 'إلغاء';

  @override
  String get confirm => 'تأكيد';

  @override
  String get networkError => 'خطأ في الاتصال بالشبكة';

  @override
  String get authError => 'خطأ في المصادقة';

  @override
  String get validationError => 'خطأ في التحقق من البيانات';
}
