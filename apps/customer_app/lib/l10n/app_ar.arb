{"@@locale": "ar", "appTitle": "تطبيق العملاء", "@appTitle": {"description": "The title of the customer application"}, "login": "تسجيل الدخول", "@login": {"description": "Login button text"}, "email": "الب<PERSON>يد الإلكتروني", "@email": {"description": "Email field label"}, "loading": "جاري التحميل...", "@loading": {"description": "Loading indicator text"}, "error": "خطأ", "@error": {"description": "Generic error text"}, "retry": "إعادة المحاولة", "@retry": {"description": "Retry button text"}, "products": "المنتجات", "@products": {"description": "Products section title"}, "search": "البحث", "@search": {"description": "Search functionality"}, "cart": "السلة", "@cart": {"description": "Shopping cart"}, "orders": "الطلبات", "@orders": {"description": "Orders section"}, "profile": "الملف الشخصي", "@profile": {"description": "User profile"}, "addToCart": "إضافة للسلة", "@addToCart": {"description": "Add to cart button"}, "checkout": "إتمام الطلب", "@checkout": {"description": "Checkout button"}, "orderHistory": "تاريخ الطلبات", "@orderHistory": {"description": "Order history section"}, "trackOrder": "تتبع الطلب", "@trackOrder": {"description": "Track order functionality"}, "cashOnDelivery": "الدفع عند الاستلام", "@cashOnDelivery": {"description": "Cash on delivery payment method"}, "deliveryAddress": "عنوان التوصيل", "@deliveryAddress": {"description": "Delivery address"}, "total": "المجموع", "@total": {"description": "Total amount"}, "cancel": "إلغاء", "@cancel": {"description": "Cancel button"}, "confirm": "تأكيد", "@confirm": {"description": "Confirm button"}, "networkError": "خطأ في الاتصال بالشبكة", "@networkError": {"description": "Network connection error message"}, "authError": "خطأ في المصادقة", "@authError": {"description": "Authentication error message"}, "validationError": "خطأ في التحقق من البيانات", "@validationError": {"description": "Validation error message"}}