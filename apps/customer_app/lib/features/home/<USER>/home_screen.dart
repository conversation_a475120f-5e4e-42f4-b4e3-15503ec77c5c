import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:auth_service/auth_service.dart';
import '../../../l10n/generated/app_localizations.dart';

/// Home screen for authenticated customers
class HomeScreen extends ConsumerWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context)!;
    final authState = ref.watch(authStateProvider);
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.appTitle),
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) async {
              if (value == 'logout') {
                final authNotifier = ref.read(authStateProvider.notifier);
                await authNotifier.signOut();
                if (context.mounted) {
                  context.go('/login');
                }
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'logout',
                child: Row(
                  children: [
                    Icon(Icons.logout),
                    SizedBox(width: 8),
                    Text('تسجيل الخروج'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _buildContent(context, l10n, authState),
    );
  }

  /// Build content based on auth state
  Widget _buildContent(
      BuildContext context, AppLocalizations l10n, AuthState authState) {
    if (authState.isLoading) {
      return _buildLoadingContent(context, l10n);
    } else if (authState.error != null) {
      return _buildErrorContent(context, l10n, authState.error!);
    } else if (authState.isAuthenticated && authState.user != null) {
      return _buildHomeContent(context, l10n, authState.user!);
    } else {
      return _buildNotAuthenticatedContent(context, l10n);
    }
  }

  /// Build content for authenticated users
  Widget _buildHomeContent(BuildContext context, AppLocalizations l10n, user) {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome Section
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20.0),
            decoration: BoxDecoration(
              color: theme.colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'مرحباً ${user.name}',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    color: theme.colorScheme.onPrimaryContainer,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'اكتشف أفضل المنتجات من البائعين المحليين',
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: theme.colorScheme.onPrimaryContainer,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Quick Actions
          Text(
            'الإجراءات السريعة',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            children: [
              _buildQuickActionCard(
                context,
                icon: Icons.shopping_bag,
                title: l10n.products,
                subtitle: 'تصفح المنتجات',
                onTap: () {
                  // TODO: Navigate to products screen
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('قريباً: تصفح المنتجات')),
                  );
                },
              ),
              _buildQuickActionCard(
                context,
                icon: Icons.shopping_cart,
                title: l10n.cart,
                subtitle: 'عرض السلة',
                onTap: () {
                  // TODO: Navigate to cart screen
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('قريباً: عرض السلة')),
                  );
                },
              ),
              _buildQuickActionCard(
                context,
                icon: Icons.receipt_long,
                title: l10n.orders,
                subtitle: 'طلباتي',
                onTap: () {
                  // TODO: Navigate to orders screen
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('قريباً: عرض الطلبات')),
                  );
                },
              ),
              _buildQuickActionCard(
                context,
                icon: Icons.person,
                title: l10n.profile,
                subtitle: 'الملف الشخصي',
                onTap: () {
                  // TODO: Navigate to profile screen
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('قريباً: الملف الشخصي')),
                  );
                },
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Featured Section
          Text(
            'المميز',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          Container(
            width: double.infinity,
            height: 200,
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceVariant,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.store,
                    size: 64,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'قريباً: المنتجات المميزة',
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build quick action card
  Widget _buildQuickActionCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);

    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 32,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build loading content
  Widget _buildLoadingContent(BuildContext context, AppLocalizations l10n) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(),
          const SizedBox(height: 16),
          Text(l10n.loading),
        ],
      ),
    );
  }

  /// Build not authenticated content
  Widget _buildNotAuthenticatedContent(
      BuildContext context, AppLocalizations l10n) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.login, size: 64),
          const SizedBox(height: 16),
          Text('يرجى تسجيل الدخول أولاً'),
          const SizedBox(height: 16),
          FilledButton(
            onPressed: () => context.go('/login'),
            child: Text(l10n.login),
          ),
        ],
      ),
    );
  }

  /// Build error content
  Widget _buildErrorContent(
      BuildContext context, AppLocalizations l10n, String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error,
              size: 64, color: Theme.of(context).colorScheme.error),
          const SizedBox(height: 16),
          Text(error),
          const SizedBox(height: 16),
          FilledButton(
            onPressed: () => context.go('/login'),
            child: Text(l10n.retry),
          ),
        ],
      ),
    );
  }
}
