import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:auth_service/auth_service.dart';
import 'package:core/core.dart';
import '../../../l10n/generated/app_localizations.dart';

/// Login screen for customer authentication using supabase_auth_ui
class LoginScreen extends ConsumerWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final l10n = AppLocalizations.of(context)!;
    final authState = ref.watch(authStateProvider);

    // Listen to auth state changes and navigate when authenticated
    ref.listen<AuthState>(authStateProvider, (previous, next) {
      if (next.isAuthenticated && next.user?.type == UserType.customer) {
        // Navigate to home screen when customer is authenticated
        context.go('/home');
      }
    });

    return Scaffold(
      body: SafeArea(
        child: _buildContent(context, l10n, authState),
      ),
    );
  }

  /// Build content based on auth state
  Widget _buildContent(
      BuildContext context, AppLocalizations l10n, AuthState authState) {
    if (authState.isLoading) {
      return _buildLoadingContent(context, l10n);
    } else if (authState.error != null) {
      return _buildErrorContent(context, l10n, authState.error!);
    } else if (authState.isAuthenticated && authState.user != null) {
      return _buildAuthenticatedContent(context, l10n, authState.user!);
    } else {
      return _buildLoginContent(context, l10n);
    }
  }

  /// Build the main login content
  Widget _buildLoginContent(BuildContext context, AppLocalizations l10n) {
    return Column(
      children: [
        _buildHeader(context, l10n),
        Expanded(
          child: AuthUIWrapper(
            userType: UserType.customer,
            onSignInComplete: () {
              // Navigation is handled by the auth state listener
            },
          ),
        ),
      ],
    );
  }

  /// Build loading state content
  Widget _buildLoadingContent(BuildContext context, AppLocalizations l10n) {
    return Column(
      children: [
        _buildHeader(context, l10n),
        Expanded(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(
                  l10n.loading,
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Build authenticated state content (should not be visible due to navigation)
  Widget _buildAuthenticatedContent(
    BuildContext context,
    AppLocalizations l10n,
    User user,
  ) {
    return Column(
      children: [
        _buildHeader(context, l10n),
        Expanded(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.check_circle,
                  size: 64,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(height: 16),
                Text(
                  'مرحباً ${user.name}',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  'تم تسجيل الدخول بنجاح',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Build error state content
  Widget _buildErrorContent(
    BuildContext context,
    AppLocalizations l10n,
    String error,
  ) {
    return Column(
      children: [
        _buildHeader(context, l10n),
        Expanded(
          child: Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    l10n.error,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          color: Theme.of(context).colorScheme.error,
                        ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    error,
                    style: Theme.of(context).textTheme.bodyLarge,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  FilledButton(
                    onPressed: () {
                      // Clear error and try again
                      context.go('/login');
                    },
                    child: Text(l10n.retry),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Build the header section
  Widget _buildHeader(BuildContext context, AppLocalizations l10n) {
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24.0),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(32),
          bottomRight: Radius.circular(32),
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.shopping_bag,
            size: 64,
            color: theme.colorScheme.onPrimaryContainer,
          ),
          const SizedBox(height: 16),
          Text(
            l10n.appTitle,
            style: theme.textTheme.headlineMedium?.copyWith(
              color: theme.colorScheme.onPrimaryContainer,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'مرحباً بك في منصة التسوق',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onPrimaryContainer,
            ),
          ),
        ],
      ),
    );
  }
}
