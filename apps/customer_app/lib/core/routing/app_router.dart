import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:auth_service/auth_service.dart';
import 'package:core/core.dart';
import '../../features/auth/screens/splash_screen.dart';
import '../../features/auth/screens/login_screen.dart';
import '../../features/home/<USER>/home_screen.dart';

/// Provider for the app router
final appRouterProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: '/',
    debugLogDiagnostics: true,
    redirect: (context, state) {
      final authState = ref.read(authStateProvider);
      final isGoingToLogin = state.matchedLocation == '/login';
      final isGoingToSplash = state.matchedLocation == '/';

      if (authState.isLoading) {
        // Still checking auth status
        if (isGoingToSplash) {
          return null; // Stay on splash while loading
        }
        return '/'; // Redirect to splash if not already there
      } else if (authState.error != null) {
        // Error state, redirect to login
        if (isGoingToLogin || isGoingToSplash) {
          return null;
        }
        return '/login';
      } else if (authState.isAuthenticated && authState.user != null) {
        // Authenticated
        if (authState.user!.type != UserType.customer) {
          // Wrong user type, redirect to login
          return '/login';
        }

        if (isGoingToLogin || isGoingToSplash) {
          return '/home'; // Redirect authenticated users away from login/splash
        }
        return null; // Allow access to protected routes
      } else {
        // Not authenticated
        if (isGoingToLogin || isGoingToSplash) {
          return null; // Allow access to login and splash
        }
        return '/login'; // Redirect to login for protected routes
      }
    },
    routes: [
      GoRoute(
        path: '/',
        name: 'splash',
        builder: (context, state) => const SplashScreen(),
      ),
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/home',
        name: 'home',
        builder: (context, state) => const HomeScreen(),
      ),
      // TODO: Add more routes for products, cart, orders, profile, etc.
    ],
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(
        title: const Text('خطأ'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64),
            const SizedBox(height: 16),
            const Text('الصفحة غير موجودة'),
            const SizedBox(height: 16),
            FilledButton(
              onPressed: () => context.go('/'),
              child: const Text('العودة للرئيسية'),
            ),
          ],
        ),
      ),
    ),
  );
});
