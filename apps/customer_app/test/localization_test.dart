import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:customer_app/l10n/generated/app_localizations.dart';
import 'package:core/core.dart';

void main() {
  group('Arabic Localization Tests', () {
    testWidgets('should display Arabic text correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [Locale('ar')],
          locale: const Locale('ar'),
          home: Scaffold(
            appBar: AppBar(
              title: Builder(
                builder: (context) =>
                    Text(AppLocalizations.of(context)!.appTitle),
              ),
            ),
            body: Builder(
              builder: (context) => Column(
                children: [
                  Text(AppLocalizations.of(context)!.login),
                  Text(AppLocalizations.of(context)!.email),
                  Text(AppLocalizations.of(context)!.loading),
                ],
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify Arabic text is displayed
      expect(find.text('تطبيق العملاء'), findsOneWidget);
      expect(find.text('تسجيل الدخول'), findsOneWidget);
      expect(find.text('البريد الإلكتروني'), findsOneWidget);
      expect(find.text('جاري التحميل...'), findsOneWidget);
    });

    testWidgets('should use RTL text direction for Arabic',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [Locale('ar')],
          locale: const Locale('ar'),
          home: Scaffold(
            body: Builder(
              builder: (context) => Text(
                'مرحبا بك في التطبيق',
                textDirection: TextDirection.rtl,
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find the Text widget
      final textWidget = tester.widget<Text>(find.text('مرحبا بك في التطبيق'));

      // Verify RTL text direction
      expect(textWidget.textDirection, TextDirection.rtl);
    });

    testWidgets('should handle Arabic text in forms correctly',
        (WidgetTester tester) async {
      final controller = TextEditingController();

      await tester.pumpWidget(
        MaterialApp(
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [Locale('ar')],
          locale: const Locale('ar'),
          home: Scaffold(
            body: Builder(
              builder: (context) => TextField(
                controller: controller,
                decoration: InputDecoration(
                  labelText: AppLocalizations.of(context)!.email,
                ),
                textDirection: TextDirection.rtl,
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify label text is in Arabic
      expect(find.text('البريد الإلكتروني'), findsOneWidget);

      // Test Arabic input
      await tester.enterText(find.byType(TextField), 'مستخدم@example.com');
      expect(controller.text, 'مستخدم@example.com');
    });

    testWidgets('should display error messages in Arabic',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [Locale('ar')],
          locale: const Locale('ar'),
          home: Scaffold(
            body: Builder(
              builder: (context) => Column(
                children: [
                  Text(AppLocalizations.of(context)!.networkError),
                  Text(AppLocalizations.of(context)!.authError),
                  Text(AppLocalizations.of(context)!.validationError),
                ],
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify Arabic error messages
      expect(find.text('خطأ في الاتصال بالشبكة'), findsOneWidget);
      expect(find.text('خطأ في المصادقة'), findsOneWidget);
      expect(find.text('خطأ في التحقق من البيانات'), findsOneWidget);
    });
  });

  group('Core Localization Tests', () {
    testWidgets('should access core localizations correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          localizationsDelegates:
              AppLocalizationsDelegate.localizationsDelegates,
          supportedLocales: AppLocalizationsDelegate.supportedLocales,
          locale: const Locale('ar'),
          home: Scaffold(
            body: Builder(
              builder: (context) => Column(
                children: [
                  Text(context.coreL10n.yes),
                  Text(context.coreL10n.no),
                  Text(context.coreL10n.loading),
                  Text(context.coreL10n.error),
                ],
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify core Arabic translations
      expect(find.text('نعم'), findsOneWidget);
      expect(find.text('لا'), findsOneWidget);
      expect(find.text('جاري التحميل...'), findsOneWidget);
      expect(find.text('خطأ'), findsOneWidget);
    });
  });

  group('Localization Delegate Tests', () {
    test('should support Arabic locale', () {
      const delegate = AppLocalizationsDelegate();

      expect(delegate.isSupported(const Locale('ar')), isTrue);
      expect(delegate.isSupported(const Locale('en')), isFalse);
      expect(delegate.isSupported(const Locale('fr')), isFalse);
    });

    test('should not reload delegate unnecessarily', () {
      const delegate1 = AppLocalizationsDelegate();
      const delegate2 = AppLocalizationsDelegate();

      expect(delegate1.shouldReload(delegate2), isFalse);
    });
  });
}
