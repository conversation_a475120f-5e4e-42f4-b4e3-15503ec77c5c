// Mocks generated by <PERSON><PERSON>to 5.4.6 from annotations
// in customer_app/test/features/auth/login_screen_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:auth_service/models/auth_result.dart' as _i5;
import 'package:auth_service/repositories/auth_repository.dart' as _i2;
import 'package:core/models/user.dart' as _i4;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i6;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [AuthRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthRepository extends _i1.Mock implements _i2.AuthRepository {
  MockAuthRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Stream<_i4.User?> get authStateChanges => (super.noSuchMethod(
        Invocation.getter(#authStateChanges),
        returnValue: _i3.Stream<_i4.User?>.empty(),
      ) as _i3.Stream<_i4.User?>);

  @override
  _i3.Future<_i5.AuthResult> sendMagicLink(String? email) =>
      (super.noSuchMethod(
        Invocation.method(
          #sendMagicLink,
          [email],
        ),
        returnValue:
            _i3.Future<_i5.AuthResult>.value(_i6.dummyValue<_i5.AuthResult>(
          this,
          Invocation.method(
            #sendMagicLink,
            [email],
          ),
        )),
      ) as _i3.Future<_i5.AuthResult>);

  @override
  _i3.Future<_i5.AuthResult> verifyMagicLink(String? token) =>
      (super.noSuchMethod(
        Invocation.method(
          #verifyMagicLink,
          [token],
        ),
        returnValue:
            _i3.Future<_i5.AuthResult>.value(_i6.dummyValue<_i5.AuthResult>(
          this,
          Invocation.method(
            #verifyMagicLink,
            [token],
          ),
        )),
      ) as _i3.Future<_i5.AuthResult>);

  @override
  _i3.Future<_i4.User?> getCurrentUser() => (super.noSuchMethod(
        Invocation.method(
          #getCurrentUser,
          [],
        ),
        returnValue: _i3.Future<_i4.User?>.value(),
      ) as _i3.Future<_i4.User?>);

  @override
  _i3.Future<_i5.AuthResult> signOut() => (super.noSuchMethod(
        Invocation.method(
          #signOut,
          [],
        ),
        returnValue:
            _i3.Future<_i5.AuthResult>.value(_i6.dummyValue<_i5.AuthResult>(
          this,
          Invocation.method(
            #signOut,
            [],
          ),
        )),
      ) as _i3.Future<_i5.AuthResult>);
}
