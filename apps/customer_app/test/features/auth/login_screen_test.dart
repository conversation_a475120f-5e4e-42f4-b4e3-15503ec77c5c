import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:auth_service/auth_service.dart';
import 'package:core/core.dart';
import 'package:customer_app/features/auth/screens/login_screen.dart';
import 'package:customer_app/l10n/generated/app_localizations.dart';

// Generate mocks
@GenerateMocks([AuthRepository])
import 'login_screen_test.mocks.dart';

void main() {
  group('LoginScreen', () {
    late MockAuthRepository mockAuthRepository;

    setUp(() {
      mockAuthRepository = MockAuthRepository();
      
      // Mock the auth state changes stream
      when(mockAuthRepository.authStateChanges)
          .thenAnswer((_) => Stream.value(null));
      
      // Mock getCurrentUser to return null initially
      when(mockAuthRepository.getCurrentUser()).thenAnswer((_) async => null);
    });

    Widget createTestWidget({AuthState? initialAuthState}) {
      return ProviderScope(
        overrides: [
          authRepositoryProvider.overrideWithValue(mockAuthRepository),
        ],
        child: MaterialApp(
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [Locale('ar')],
          locale: const Locale('ar'),
          home: const LoginScreen(),
        ),
      );
    }

    testWidgets('should display login screen with header', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Verify header elements are displayed
      expect(find.byIcon(Icons.shopping_bag), findsOneWidget);
      expect(find.text('تطبيق العملاء'), findsOneWidget);
      expect(find.text('مرحباً بك في منصة التسوق'), findsOneWidget);
    });

    testWidgets('should display loading state when auth is loading', (WidgetTester tester) async {
      // Mock loading state
      when(mockAuthRepository.authStateChanges)
          .thenAnswer((_) => const Stream.empty());
      when(mockAuthRepository.getCurrentUser())
          .thenAnswer((_) => Future.delayed(const Duration(seconds: 1), () => null));

      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Initial build
      
      // Should show loading indicator
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('جاري التحميل...'), findsOneWidget);
    });

    testWidgets('should display error state when auth fails', (WidgetTester tester) async {
      // Mock error state
      when(mockAuthRepository.authStateChanges)
          .thenAnswer((_) => Stream.error('Test error'));

      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Should show error UI
      expect(find.byIcon(Icons.error_outline), findsOneWidget);
      expect(find.text('خطأ'), findsOneWidget);
      expect(find.text('إعادة المحاولة'), findsOneWidget);
    });

    testWidgets('should display AuthUIWrapper when not authenticated', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Should show AuthUIWrapper component
      expect(find.byType(AuthUIWrapper), findsOneWidget);
    });

    testWidgets('should display authenticated content when user is authenticated', (WidgetTester tester) async {
      final testUser = User(
        id: 'test-id',
        email: '<EMAIL>',
        name: 'Test User',
        type: UserType.customer,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Mock authenticated state
      when(mockAuthRepository.authStateChanges)
          .thenAnswer((_) => Stream.value(testUser));
      when(mockAuthRepository.getCurrentUser())
          .thenAnswer((_) async => testUser);

      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Should show authenticated content
      expect(find.byIcon(Icons.check_circle), findsOneWidget);
      expect(find.text('مرحباً ${testUser.name}'), findsOneWidget);
      expect(find.text('تم تسجيل الدخول بنجاح'), findsOneWidget);
    });

    testWidgets('should handle retry button tap in error state', (WidgetTester tester) async {
      // Mock error state
      when(mockAuthRepository.authStateChanges)
          .thenAnswer((_) => Stream.error('Test error'));

      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Find and tap retry button
      final retryButton = find.text('إعادة المحاولة');
      expect(retryButton, findsOneWidget);
      
      await tester.tap(retryButton);
      await tester.pumpAndSettle();

      // Note: In a real app, this would navigate back to login
      // Here we just verify the button is tappable
    });
  });
}
