import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:vendor_app/l10n/generated/app_localizations.dart';

void main() {
  group('Vendor App Arabic Localization Tests', () {
    testWidgets('should display vendor-specific Arabic text correctly',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [Locale('ar')],
          locale: const Locale('ar'),
          home: Scaffold(
            appBar: AppBar(
              title: Builder(
                builder: (context) =>
                    Text(AppLocalizations.of(context)!.appTitle),
              ),
            ),
            body: Builder(
              builder: (context) => Column(
                children: [
                  Text(AppLocalizations.of(context)!.inventory),
                  Text(AppLocalizations.of(context)!.addProduct),
                  Text(AppLocalizations.of(context)!.businessName),
                  Text(AppLocalizations.of(context)!.sales),
                ],
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify vendor-specific Arabic text
      expect(find.text('تطبيق التجار'), findsOneWidget);
      expect(find.text('المخزون'), findsOneWidget);
      expect(find.text('إضافة منتج'), findsOneWidget);
      expect(find.text('اسم النشاط التجاري'), findsOneWidget);
      expect(find.text('المبيعات'), findsOneWidget);
    });

    testWidgets('should handle vendor form inputs in Arabic',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [Locale('ar')],
          locale: const Locale('ar'),
          home: Scaffold(
            body: Builder(
              builder: (context) => Column(
                children: [
                  TextField(
                    decoration: InputDecoration(
                      labelText: AppLocalizations.of(context)!.productName,
                    ),
                    textDirection: TextDirection.rtl,
                  ),
                  TextField(
                    decoration: InputDecoration(
                      labelText:
                          AppLocalizations.of(context)!.productDescription,
                    ),
                    textDirection: TextDirection.rtl,
                  ),
                  TextField(
                    decoration: InputDecoration(
                      labelText: AppLocalizations.of(context)!.price,
                    ),
                    textDirection: TextDirection.rtl,
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify Arabic form labels
      expect(find.text('اسم المنتج'), findsOneWidget);
      expect(find.text('وصف المنتج'), findsOneWidget);
      expect(find.text('السعر'), findsOneWidget);
    });

    testWidgets('should display vendor order management in Arabic',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [Locale('ar')],
          locale: const Locale('ar'),
          home: Scaffold(
            body: Builder(
              builder: (context) => Column(
                children: [
                  Text(AppLocalizations.of(context)!.newOrders),
                  ElevatedButton(
                    onPressed: () {},
                    child: Text(AppLocalizations.of(context)!.acceptOrder),
                  ),
                  TextButton(
                    onPressed: () {},
                    child: Text(AppLocalizations.of(context)!.rejectOrder),
                  ),
                  Text(AppLocalizations.of(context)!.orderReady),
                ],
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify order management Arabic text
      expect(find.text('طلبات جديدة'), findsOneWidget);
      expect(find.text('قبول الطلب'), findsOneWidget);
      expect(find.text('رفض الطلب'), findsOneWidget);
      expect(find.text('الطلب جاهز'), findsOneWidget);
    });
  });
}
