name: vendor_app
description: Vendor app for multi-vendor ecommerce platform
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  
  # State Management
  flutter_riverpod: ^2.4.9
  riverpod_annotation: ^2.3.3
  
  # UI & Design
  material_color_utilities: ^0.11.1
  
  # Navigation
  go_router: ^13.2.0
  
  # Networking & Backend
  supabase_flutter: ^2.3.4
  http: ^1.2.0
  
  # Image Handling
  cached_network_image: ^3.3.1
  flutter_blurhash: ^0.8.2
  image_picker: ^1.0.7
  
  # Charts & Analytics
  fl_chart: ^0.66.2
  
  # Utilities
  intl: ^0.20.2
  json_annotation: ^4.8.1
  equatable: ^2.0.5
  
  # Shared Packages
  core:
    path: ../../packages/core
  ui_components:
    path: ../../packages/ui_components
  auth_service:
    path: ../../packages/auth_service
  api_client:
    path: ../../packages/api_client

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0
  
  # Code Generation
  build_runner: ^2.4.7
  riverpod_generator: ^2.3.9
  json_serializable: ^6.7.1
  
  # Testing
  mockito: ^5.4.4
  golden_toolkit: ^0.15.0

flutter:
  uses-material-design: true
  generate: true