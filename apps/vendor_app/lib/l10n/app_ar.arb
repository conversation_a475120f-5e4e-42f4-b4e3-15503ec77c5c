{"@@locale": "ar", "appTitle": "تطبيق التجار", "@appTitle": {"description": "The title of the vendor application"}, "login": "تسجيل الدخول", "@login": {"description": "Login button text"}, "email": "الب<PERSON>يد الإلكتروني", "@email": {"description": "Email field label"}, "loading": "جاري التحميل...", "@loading": {"description": "Loading indicator text"}, "error": "خطأ", "@error": {"description": "Generic error text"}, "retry": "إعادة المحاولة", "@retry": {"description": "Retry button text"}, "inventory": "المخزون", "@inventory": {"description": "Inventory management section"}, "orders": "الطلبات", "@orders": {"description": "Orders section"}, "analytics": "التحليلات", "@analytics": {"description": "Analytics section"}, "profile": "الملف الشخصي", "@profile": {"description": "Vendor profile"}, "addProduct": "إضافة منتج", "@addProduct": {"description": "Add product button"}, "editProduct": "تعديل المنتج", "@editProduct": {"description": "Edit product button"}, "deleteProduct": "<PERSON><PERSON><PERSON> المنتج", "@deleteProduct": {"description": "Delete product button"}, "productName": "اسم المنتج", "@productName": {"description": "Product name field"}, "productDescription": "وصف المنتج", "@productDescription": {"description": "Product description field"}, "price": "السعر", "@price": {"description": "Price field"}, "stock": "الكمية المتوفرة", "@stock": {"description": "Stock quantity"}, "category": "الفئة", "@category": {"description": "Product category"}, "newOrders": "طلبات جديدة", "@newOrders": {"description": "New orders section"}, "acceptOrder": "قبول الطلب", "@acceptOrder": {"description": "Accept order button"}, "rejectOrder": "<PERSON><PERSON><PERSON> الطلب", "@rejectOrder": {"description": "Reject order button"}, "orderReady": "الطلب جاهز", "@orderReady": {"description": "Order ready status"}, "sales": "المبيعات", "@sales": {"description": "Sales analytics"}, "revenue": "الإيرادات", "@revenue": {"description": "Revenue analytics"}, "businessName": "اسم النشاط التجاري", "@businessName": {"description": "Business name field"}, "businessAddress": "عنوان النشاط التجاري", "@businessAddress": {"description": "Business address field"}, "save": "<PERSON><PERSON><PERSON>", "@save": {"description": "Save button"}, "cancel": "إلغاء", "@cancel": {"description": "Cancel button"}, "confirm": "تأكيد", "@confirm": {"description": "Confirm button"}, "networkError": "خطأ في الاتصال بالشبكة", "@networkError": {"description": "Network connection error message"}, "authError": "خطأ في المصادقة", "@authError": {"description": "Authentication error message"}, "validationError": "خطأ في التحقق من البيانات", "@validationError": {"description": "Validation error message"}}