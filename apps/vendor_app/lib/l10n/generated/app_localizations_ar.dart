// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'تطبيق التجار';

  @override
  String get login => 'تسجيل الدخول';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get loading => 'جاري التحميل...';

  @override
  String get error => 'خطأ';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get inventory => 'المخزون';

  @override
  String get orders => 'الطلبات';

  @override
  String get analytics => 'التحليلات';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get addProduct => 'إضافة منتج';

  @override
  String get editProduct => 'تعديل المنتج';

  @override
  String get deleteProduct => 'حذف المنتج';

  @override
  String get productName => 'اسم المنتج';

  @override
  String get productDescription => 'وصف المنتج';

  @override
  String get price => 'السعر';

  @override
  String get stock => 'الكمية المتوفرة';

  @override
  String get category => 'الفئة';

  @override
  String get newOrders => 'طلبات جديدة';

  @override
  String get acceptOrder => 'قبول الطلب';

  @override
  String get rejectOrder => 'رفض الطلب';

  @override
  String get orderReady => 'الطلب جاهز';

  @override
  String get sales => 'المبيعات';

  @override
  String get revenue => 'الإيرادات';

  @override
  String get businessName => 'اسم النشاط التجاري';

  @override
  String get businessAddress => 'عنوان النشاط التجاري';

  @override
  String get save => 'حفظ';

  @override
  String get cancel => 'إلغاء';

  @override
  String get confirm => 'تأكيد';

  @override
  String get networkError => 'خطأ في الاتصال بالشبكة';

  @override
  String get authError => 'خطأ في المصادقة';

  @override
  String get validationError => 'خطأ في التحقق من البيانات';
}
