import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:customer_app/main.dart' as customer_app;
import 'package:vendor_app/main.dart' as vendor_app;
import 'package:courier_app/main.dart' as courier_app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Arabic Localization Integration Tests', () {
    testWidgets('Customer app should launch with Arabic localization',
        (WidgetTester tester) async {
      customer_app.main();
      await tester.pumpAndSettle();

      // Verify the app launches and displays Arabic text
      expect(find.text('تطبيق العملاء'), findsOneWidget);
    });

    testWidgets('Vendor app should launch with Arabic localization',
        (WidgetTester tester) async {
      vendor_app.main();
      await tester.pumpAndSettle();

      // Verify the app launches and displays Arabic text
      expect(find.text('تطبيق التجار'), findsOneWidget);
    });

    testWidgets('Courier app should launch with Arabic localization',
        (WidgetTester tester) async {
      courier_app.main();
      await tester.pumpAndSettle();

      // Verify the app launches and displays Arabic text
      expect(find.text('تطبيق المندوبين'), findsOneWidget);
    });

    testWidgets('Apps should handle RTL layout correctly',
        (WidgetTester tester) async {
      customer_app.main();
      await tester.pumpAndSettle();

      // Find the MaterialApp widget
      final materialApp = tester.widget<MaterialApp>(find.byType(MaterialApp));

      // Verify Arabic locale is set
      expect(materialApp.locale, const Locale('ar'));

      // Verify RTL support is configured
      expect(materialApp.localizationsDelegates, isNotEmpty);
      expect(materialApp.supportedLocales, contains(const Locale('ar')));
    });

    testWidgets('Text direction should be RTL for Arabic content',
        (WidgetTester tester) async {
      customer_app.main();
      await tester.pumpAndSettle();

      // Create a test widget with Arabic text
      await tester.pumpWidget(
        MaterialApp(
          locale: const Locale('ar'),
          home: Scaffold(
            body: Directionality(
              textDirection: TextDirection.rtl,
              child: Text(
                'النص العربي يجب أن يكون من اليمين إلى اليسار',
                textDirection: TextDirection.rtl,
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find the text widget and verify RTL direction
      final textWidget = tester.widget<Text>(
        find.text('النص العربي يجب أن يكون من اليمين إلى اليسار'),
      );
      expect(textWidget.textDirection, TextDirection.rtl);
    });
  });
}
