import 'package:flutter/material.dart';

/// Custom card variants
enum AppCardVariant {
  elevated,
  filled,
  outlined,
}

/// Reusable card component with Material 3 styling
class AppCard extends StatelessWidget {
  const AppCard({
    super.key,
    required this.child,
    this.variant = AppCardVariant.elevated,
    this.onTap,
    this.padding,
    this.margin,
    this.borderRadius,
  });

  final Widget child;
  final AppCardVariant variant;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final BorderRadius? borderRadius;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final defaultBorderRadius = borderRadius ?? BorderRadius.circular(12);
    final defaultPadding = padding ?? const EdgeInsets.all(16);

    Widget card = switch (variant) {
      AppCardVariant.elevated => Card(
          elevation: 1,
          color: colorScheme.surface,
          shape: RoundedRectangleBorder(borderRadius: defaultBorderRadius),
          child: Padding(padding: defaultPadding, child: child),
        ),
      AppCardVariant.filled => Card(
          elevation: 0,
          color: colorScheme.surfaceVariant,
          shape: RoundedRectangleBorder(borderRadius: defaultBorderRadius),
          child: Padding(padding: defaultPadding, child: child),
        ),
      AppCardVariant.outlined => Card(
          elevation: 0,
          color: colorScheme.surface,
          shape: RoundedRectangleBorder(
            borderRadius: defaultBorderRadius,
            side: BorderSide(color: colorScheme.outline),
          ),
          child: Padding(padding: defaultPadding, child: child),
        ),
    };

    if (onTap != null) {
      card = InkWell(
        onTap: onTap,
        borderRadius: defaultBorderRadius,
        child: card,
      );
    }

    return margin != null ? Padding(padding: margin!, child: card) : card;
  }
}

/// Product card component for displaying product information
class ProductCard extends StatelessWidget {
  const ProductCard({
    super.key,
    required this.imageUrl,
    required this.title,
    required this.price,
    this.blurHash,
    this.onTap,
    this.onFavoritePressed,
    this.isFavorite = false,
  });

  final String imageUrl;
  final String title;
  final double price;
  final String? blurHash;
  final VoidCallback? onTap;
  final VoidCallback? onFavoritePressed;
  final bool isFavorite;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return AppCard(
      onTap: onTap,
      padding: EdgeInsets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product image with favorite button
          Stack(
            children: [
              ClipRRect(
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(12)),
                child: AspectRatio(
                  aspectRatio: 1,
                  child: Container(
                    width: double.infinity,
                    color: colorScheme.surfaceVariant,
                    child: const Icon(
                      Icons.image,
                      size: 48,
                      color: Colors.grey,
                    ),
                  ),
                ),
              ),
              if (onFavoritePressed != null)
                Positioned(
                  top: 8,
                  right: 8,
                  child: IconButton(
                    onPressed: onFavoritePressed,
                    icon: Icon(
                      isFavorite ? Icons.favorite : Icons.favorite_border,
                      color: isFavorite ? Colors.red : colorScheme.onSurface,
                    ),
                    style: IconButton.styleFrom(
                      backgroundColor: colorScheme.surface.withOpacity(0.8),
                      padding: const EdgeInsets.all(8),
                    ),
                  ),
                ),
            ],
          ),

          // Product details
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleSmall,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  '${price.toStringAsFixed(2)} ر.س',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
