import 'package:flutter/material.dart';

/// Custom button variants following Material 3 design
enum AppButtonVariant {
  filled,
  elevated,
  outlined,
  text,
}

/// Custom button sizes
enum AppButtonSize {
  small,
  medium,
  large,
}

/// Reusable button component with Material 3 styling
class AppButton extends StatelessWidget {
  const AppButton({
    super.key,
    required this.onPressed,
    required this.child,
    this.variant = AppButtonVariant.filled,
    this.size = AppButtonSize.medium,
    this.isLoading = false,
    this.isFullWidth = false,
    this.icon,
  });

  final VoidCallback? onPressed;
  final Widget child;
  final AppButtonVariant variant;
  final AppButtonSize size;
  final bool isLoading;
  final bool isFullWidth;
  final Widget? icon;

  @override
  Widget build(BuildContext context) {
    final buttonChild = isLoading
        ? SizedBox(
            height: _getIconSize(),
            width: _getIconSize(),
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                _getLoadingColor(context),
              ),
            ),
          )
        : icon != null
            ? Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  icon!,
                  const SizedBox(width: 8),
                  child,
                ],
              )
            : child;

    final button = switch (variant) {
      AppButtonVariant.filled => FilledButton(
          onPressed: isLoading ? null : onPressed,
          style: _getButtonStyle(),
          child: buttonChild,
        ),
      AppButtonVariant.elevated => ElevatedButton(
          onPressed: isLoading ? null : onPressed,
          style: _getButtonStyle(),
          child: buttonChild,
        ),
      AppButtonVariant.outlined => OutlinedButton(
          onPressed: isLoading ? null : onPressed,
          style: _getButtonStyle(),
          child: buttonChild,
        ),
      AppButtonVariant.text => TextButton(
          onPressed: isLoading ? null : onPressed,
          style: _getButtonStyle(),
          child: buttonChild,
        ),
    };

    return isFullWidth
        ? SizedBox(
            width: double.infinity,
            child: button,
          )
        : button;
  }

  ButtonStyle _getButtonStyle() {
    final padding = switch (size) {
      AppButtonSize.small =>
        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      AppButtonSize.medium =>
        const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      AppButtonSize.large =>
        const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
    };

    return ButtonStyle(
      padding: WidgetStateProperty.all(padding),
      textStyle: WidgetStateProperty.all(_getTextStyle()),
      shape: WidgetStateProperty.all(
        RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  TextStyle _getTextStyle() {
    return switch (size) {
      AppButtonSize.small =>
        const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
      AppButtonSize.medium =>
        const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
      AppButtonSize.large =>
        const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
    };
  }

  double _getIconSize() {
    return switch (size) {
      AppButtonSize.small => 16,
      AppButtonSize.medium => 18,
      AppButtonSize.large => 20,
    };
  }

  Color _getLoadingColor(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return switch (variant) {
      AppButtonVariant.filled => colorScheme.onPrimary,
      AppButtonVariant.elevated => colorScheme.primary,
      AppButtonVariant.outlined => colorScheme.primary,
      AppButtonVariant.text => colorScheme.primary,
    };
  }
}
