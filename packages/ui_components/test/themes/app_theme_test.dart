import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:ui_components/ui_components.dart';

void main() {
  group('AppTheme', () {
    test('should provide light theme with Material 3', () {
      final theme = AppTheme.lightTheme;

      expect(theme.useMaterial3, isTrue);
      expect(theme.colorScheme.brightness, Brightness.light);
      expect(theme.textTheme, isNotNull);
      expect(theme.appBarTheme, isNotNull);
      expect(theme.cardTheme, isNotNull);
    });

    test('should provide dark theme with Material 3', () {
      final theme = AppTheme.darkTheme;

      expect(theme.useMaterial3, isTrue);
      expect(theme.colorScheme.brightness, Brightness.dark);
      expect(theme.textTheme, isNotNull);
      expect(theme.appBarTheme, isNotNull);
      expect(theme.cardTheme, isNotNull);
    });

    test('should have consistent button themes', () {
      final lightTheme = AppTheme.lightTheme;
      final darkTheme = AppTheme.darkTheme;

      expect(lightTheme.elevatedButtonTheme, isNotNull);
      expect(lightTheme.filledButtonTheme, isNotNull);
      expect(lightTheme.outlinedButtonTheme, isNotNull);

      expect(darkTheme.elevatedButtonTheme, isNotNull);
      expect(darkTheme.filledButtonTheme, isNotNull);
      expect(darkTheme.outlinedButtonTheme, isNotNull);
    });

    test('should have input decoration theme configured', () {
      final theme = AppTheme.lightTheme;

      expect(theme.inputDecorationTheme, isNotNull);
      expect(theme.inputDecorationTheme.filled, isTrue);
      expect(theme.inputDecorationTheme.border, isA<OutlineInputBorder>());
    });

    test('should have Arabic-optimized text theme', () {
      final theme = AppTheme.lightTheme;
      final textTheme = theme.textTheme;

      expect(textTheme.displayLarge, isNotNull);
      expect(textTheme.headlineLarge, isNotNull);
      expect(textTheme.titleLarge, isNotNull);
      expect(textTheme.bodyLarge, isNotNull);
      expect(textTheme.labelLarge, isNotNull);

      // Check that text styles have proper font weights
      expect(textTheme.titleLarge?.fontWeight, FontWeight.w500);
      expect(textTheme.labelLarge?.fontWeight, FontWeight.w500);
    });
  });
}
