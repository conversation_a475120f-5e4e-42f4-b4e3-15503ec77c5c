import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:golden_toolkit/golden_toolkit.dart';
import 'package:ui_components/ui_components.dart';
import 'package:core/core.dart';

void main() {
  group('Golden Tests', () {
    testGoldens('AppButton variants should match golden files', (tester) async {
      final builder = DeviceBuilder()
        ..overrideDevicesForAllScenarios(devices: [Device.phone])
        ..addScenario(
          widget: MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    AppButton(
                      variant: AppButtonVariant.filled,
                      onPressed: () {},
                      child: const Text('Filled Button'),
                    ),
                    AppButton(
                      variant: AppButtonVariant.elevated,
                      onPressed: () {},
                      child: const Text('Elevated Button'),
                    ),
                    AppButton(
                      variant: AppButtonVariant.outlined,
                      onPressed: () {},
                      child: const Text('Outlined Button'),
                    ),
                    AppButton(
                      variant: AppButtonVariant.text,
                      onPressed: () {},
                      child: const Text('Text Button'),
                    ),
                    AppButton(
                      onPressed: () {},
                      isLoading: true,
                      child: const Text('Loading Button'),
                    ),
                  ],
                ),
              ),
            ),
          ),
          name: 'button_variants',
        );

      await tester.pumpDeviceBuilder(builder);
      await screenMatchesGolden(tester, 'button_variants');
    });

    testGoldens('AppCard variants should match golden files', (tester) async {
      final builder = DeviceBuilder()
        ..overrideDevicesForAllScenarios(devices: [Device.phone])
        ..addScenario(
          widget: MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: const [
                    AppCard(
                      variant: AppCardVariant.elevated,
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: Text('Elevated Card'),
                      ),
                    ),
                    SizedBox(height: 16),
                    AppCard(
                      variant: AppCardVariant.filled,
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: Text('Filled Card'),
                      ),
                    ),
                    SizedBox(height: 16),
                    AppCard(
                      variant: AppCardVariant.outlined,
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: Text('Outlined Card'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          name: 'card_variants',
        );

      await tester.pumpDeviceBuilder(builder);
      await screenMatchesGolden(tester, 'card_variants');
    });

    testGoldens('ProductCard should match golden file', (tester) async {
      final builder = DeviceBuilder()
        ..overrideDevicesForAllScenarios(devices: [Device.phone])
        ..addScenario(
          widget: MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: Padding(
                padding: const EdgeInsets.all(16),
                child: SizedBox(
                  width: 200,
                  child: ProductCard(
                    imageUrl: 'https://example.com/image.jpg',
                    title: 'منتج تجريبي',
                    price: 99.99,
                    onFavoritePressed: () {},
                    isFavorite: false,
                  ),
                ),
              ),
            ),
          ),
          name: 'product_card',
        );

      await tester.pumpDeviceBuilder(builder);
      await screenMatchesGolden(tester, 'product_card');
    });

    testGoldens('AppTextField variants should match golden files',
        (tester) async {
      final builder = DeviceBuilder()
        ..overrideDevicesForAllScenarios(devices: [Device.phone])
        ..addScenario(
          widget: MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: const [
                    AppTextField(
                      labelText: 'حقل نص عادي',
                      hintText: 'أدخل النص هنا',
                    ),
                    SizedBox(height: 16),
                    AppTextField(
                      labelText: 'حقل مع خطأ',
                      errorText: 'هذا الحقل مطلوب',
                    ),
                    SizedBox(height: 16),
                    AppSearchField(
                      hintText: 'البحث...',
                    ),
                    SizedBox(height: 16),
                    AppPasswordField(
                      labelText: 'كلمة المرور',
                    ),
                  ],
                ),
              ),
            ),
          ),
          name: 'text_field_variants',
        );

      await tester.pumpDeviceBuilder(builder);
      await screenMatchesGolden(tester, 'text_field_variants');
    });

    testGoldens('Loading widgets should match golden files', (tester) async {
      final builder = DeviceBuilder()
        ..overrideDevicesForAllScenarios(devices: [Device.phone])
        ..addScenario(
          widget: MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: const [
                    AppLoadingWidget(variant: LoadingVariant.circular),
                    AppLoadingWidget(variant: LoadingVariant.linear),
                    AppLoadingWidget(variant: LoadingVariant.dots),
                  ],
                ),
              ),
            ),
          ),
          name: 'loading_widgets',
        );

      await tester.pumpDeviceBuilder(builder);
      await screenMatchesGolden(tester, 'loading_widgets');
    });

    testGoldens('Error widgets should match golden files', (tester) async {
      const error = NetworkException('Network error');

      final builder = DeviceBuilder()
        ..overrideDevicesForAllScenarios(devices: [Device.phone])
        ..addScenario(
          widget: MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: const [
                    AppErrorWidget(
                      error: error,
                      variant: ErrorVariant.inline,
                    ),
                    SizedBox(height: 16),
                    AppErrorWidget(
                      error: error,
                      variant: ErrorVariant.card,
                    ),
                    SizedBox(height: 16),
                    AppEmptyStateWidget(
                      message: 'لا توجد عناصر',
                      icon: Icons.inbox_outlined,
                    ),
                  ],
                ),
              ),
            ),
          ),
          name: 'error_widgets',
        );

      await tester.pumpDeviceBuilder(builder);
      await screenMatchesGolden(tester, 'error_widgets');
    });

    testGoldens('Dark theme should match golden files', (tester) async {
      final builder = DeviceBuilder()
        ..overrideDevicesForAllScenarios(devices: [Device.phone])
        ..addScenario(
          widget: MaterialApp(
            theme: AppTheme.darkTheme,
            home: Scaffold(
              body: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    AppButton(
                      onPressed: () {},
                      child: const Text('Dark Theme Button'),
                    ),
                    const SizedBox(height: 16),
                    const AppCard(
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: Text('Dark Theme Card'),
                      ),
                    ),
                    const SizedBox(height: 16),
                    const AppTextField(
                      labelText: 'Dark Theme Field',
                    ),
                  ],
                ),
              ),
            ),
          ),
          name: 'dark_theme',
        );

      await tester.pumpDeviceBuilder(builder);
      await screenMatchesGolden(tester, 'dark_theme');
    });
  });
}
