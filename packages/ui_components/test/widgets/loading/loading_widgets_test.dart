import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:ui_components/ui_components.dart';

void main() {
  group('AppLoadingWidget', () {
    testWidgets('should render circular loading indicator by default',
        (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const Scaffold(
            body: AppLoadingWidget(),
          ),
        ),
      );

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should render different loading variants', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: Scaffold(
            body: Column(
              children: const [
                AppLoadingWidget(variant: LoadingVariant.circular),
                AppLoadingWidget(variant: LoadingVariant.linear),
                AppLoadingWidget(variant: LoadingVariant.dots),
              ],
            ),
          ),
        ),
      );

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.byType(LinearProgressIndicator), findsOneWidget);
      // Dots indicator is a custom widget
      expect(find.byType(AppLoadingWidget), findsNWidgets(3));
    });

    testWidgets('should apply custom size and color', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const Scaffold(
            body: AppLoadingWidget(
              size: 32.0,
              color: Colors.red,
              strokeWidth: 4.0,
            ),
          ),
        ),
      );

      final sizedBox = tester.widget<SizedBox>(find.byType(SizedBox).first);
      expect(sizedBox.width, 32.0);
      expect(sizedBox.height, 32.0);
    });
  });

  group('AppLoadingOverlay', () {
    testWidgets('should render loading overlay with message', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const Scaffold(
            body: AppLoadingOverlay(
              message: 'Loading...',
            ),
          ),
        ),
      );

      expect(find.text('Loading...'), findsOneWidget);
      expect(find.byType(Card), findsOneWidget);
      expect(find.byType(AppLoadingWidget), findsOneWidget);
    });

    testWidgets('should render without message when not provided',
        (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const Scaffold(
            body: AppLoadingOverlay(),
          ),
        ),
      );

      expect(find.byType(AppLoadingWidget), findsOneWidget);
      expect(find.byType(Card), findsOneWidget);
    });
  });

  group('AppListLoadingWidget', () {
    testWidgets('should render shimmer loading items', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const Scaffold(
            body: AppListLoadingWidget(itemCount: 3),
          ),
        ),
      );

      expect(find.byType(ListView), findsOneWidget);
      // Should find shimmer items (cards with containers)
      expect(find.byType(Card), findsNWidgets(3));
    });

    testWidgets('should use default item count when not specified',
        (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const Scaffold(
            body: AppListLoadingWidget(),
          ),
        ),
      );

      expect(find.byType(ListView), findsOneWidget);
      // Default is 5 items
      expect(find.byType(Card), findsNWidgets(5));
    });
  });
}
