import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:ui_components/ui_components.dart';

void main() {
  group('AppCard', () {
    testWidgets('should render elevated card by default', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const Scaffold(
            body: AppCard(
              child: Text('Card Content'),
            ),
          ),
        ),
      );

      expect(find.byType(Card), findsOneWidget);
      expect(find.text('Card Content'), findsOneWidget);
    });

    testWidgets('should render different card variants', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: Scaffold(
            body: Column(
              children: const [
                AppCard(
                  variant: AppCardVariant.elevated,
                  child: Text('Elevated'),
                ),
                AppCard(
                  variant: AppCardVariant.filled,
                  child: Text('Filled'),
                ),
                AppCard(
                  variant: AppCardVariant.outlined,
                  child: Text('Outlined'),
                ),
              ],
            ),
          ),
        ),
      );

      expect(find.text('Elevated'), findsOneWidget);
      expect(find.text('Filled'), findsOneWidget);
      expect(find.text('Outlined'), findsOneWidget);
    });

    testWidgets('should be tappable when onTap is provided', (tester) async {
      bool tapped = false;

      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: Scaffold(
            body: AppCard(
              onTap: () => tapped = true,
              child: const Text('Tappable Card'),
            ),
          ),
        ),
      );

      expect(find.byType(InkWell), findsOneWidget);

      await tester.tap(find.byType(AppCard));
      expect(tapped, isTrue);
    });

    testWidgets('should apply custom padding and margin', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const Scaffold(
            body: AppCard(
              padding: EdgeInsets.all(24),
              margin: EdgeInsets.all(16),
              child: Text('Custom Spacing'),
            ),
          ),
        ),
      );

      expect(find.text('Custom Spacing'), findsOneWidget);
    });
  });

  group('ProductCard', () {
    testWidgets('should render product information', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: Scaffold(
            body: SizedBox(
              width: 200,
              height: 300,
              child: ProductCard(
                imageUrl: 'https://example.com/image.jpg',
                title: 'Test Product',
                price: 99.99,
              ),
            ),
          ),
        ),
      );

      expect(find.text('Test Product'), findsOneWidget);
      expect(find.text('99.99 ر.س'), findsOneWidget);
      expect(find.byType(AspectRatio), findsOneWidget);
    });

    testWidgets('should show favorite button when callback provided',
        (tester) async {
      bool favoritePressed = false;

      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: Scaffold(
            body: SizedBox(
              width: 200,
              height: 300,
              child: ProductCard(
                imageUrl: 'https://example.com/image.jpg',
                title: 'Test Product',
                price: 99.99,
                onFavoritePressed: () => favoritePressed = true,
                isFavorite: false,
              ),
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.favorite_border), findsOneWidget);

      await tester.tap(find.byIcon(Icons.favorite_border));
      expect(favoritePressed, isTrue);
    });

    testWidgets('should show filled favorite icon when isFavorite is true',
        (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: Scaffold(
            body: SizedBox(
              width: 200,
              height: 300,
              child: ProductCard(
                imageUrl: 'https://example.com/image.jpg',
                title: 'Test Product',
                price: 99.99,
                onFavoritePressed: () {},
                isFavorite: true,
              ),
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.favorite), findsOneWidget);
    });

    testWidgets('should be tappable when onTap provided', (tester) async {
      bool tapped = false;

      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: Scaffold(
            body: SizedBox(
              width: 200,
              height: 300,
              child: ProductCard(
                imageUrl: 'https://example.com/image.jpg',
                title: 'Test Product',
                price: 99.99,
                onTap: () => tapped = true,
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.byType(ProductCard));
      expect(tapped, isTrue);
    });
  });
}
