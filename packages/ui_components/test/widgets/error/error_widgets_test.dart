import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:ui_components/ui_components.dart';
import 'package:core/core.dart';

void main() {
  group('AppErrorWidget', () {
    testWidgets('should render card error widget by default', (tester) async {
      const error = NetworkException('Network error');

      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const Scaffold(
            body: AppErrorWidget(error: error),
          ),
        ),
      );

      expect(find.byType(Card), findsOneWidget);
      expect(find.byIcon(Icons.wifi_off), findsOneWidget);
      expect(
          find.text(
              'خطأ في الاتصال بالإنترنت. يرجى التحقق من اتصالك والمحاولة مرة أخرى.'),
          findsOneWidget);
    });

    testWidgets('should render different error variants', (tester) async {
      const error = AuthException('Auth error');

      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: Scaffold(
            body: Column(
              children: const [
                AppErrorWidget(
                  error: error,
                  variant: ErrorVariant.inline,
                ),
                AppErrorWidget(
                  error: error,
                  variant: ErrorVariant.card,
                ),
                AppErrorWidget(
                  error: error,
                  variant: ErrorVariant.fullScreen,
                ),
              ],
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.lock_outline), findsNWidgets(3));
      expect(find.text('خطأ في المصادقة. يرجى تسجيل الدخول مرة أخرى.'),
          findsNWidgets(3));
    });

    testWidgets('should show retry button when onRetry is provided',
        (tester) async {
      const error = NetworkException('Network error');
      bool retryPressed = false;

      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: Scaffold(
            body: AppErrorWidget(
              error: error,
              onRetry: () => retryPressed = true,
            ),
          ),
        ),
      );

      expect(find.text('إعادة المحاولة'), findsOneWidget);

      await tester.tap(find.text('إعادة المحاولة'));
      expect(retryPressed, isTrue);
    });

    testWidgets('should hide retry button when showRetryButton is false',
        (tester) async {
      const error = NetworkException('Network error');

      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: Scaffold(
            body: AppErrorWidget(
              error: error,
              onRetry: () {},
              showRetryButton: false,
            ),
          ),
        ),
      );

      expect(find.text('إعادة المحاولة'), findsNothing);
    });

    testWidgets('should handle validation exception with field errors',
        (tester) async {
      const error =
          ValidationException('Validation failed', {'email': 'Invalid email'});

      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const Scaffold(
            body: AppErrorWidget(error: error),
          ),
        ),
      );

      expect(find.byIcon(Icons.error_outline), findsOneWidget);
      expect(find.text('البيانات المدخلة غير صحيحة. يرجى التحقق من المعلومات.'),
          findsOneWidget);
    });

    testWidgets('should handle unknown errors', (tester) async {
      const error = 'Unknown error';

      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const Scaffold(
            body: AppErrorWidget(error: error),
          ),
        ),
      );

      expect(find.byIcon(Icons.error_outline), findsOneWidget);
      expect(find.text('حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.'),
          findsOneWidget);
    });

    testWidgets('should show snackbar error', (tester) async {
      const error = NetworkException('Network error');

      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: Builder(
            builder: (context) => Scaffold(
              body: ElevatedButton(
                onPressed: () => AppErrorWidget.showSnackbar(context, error),
                child: const Text('Show Error'),
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.text('Show Error'));
      await tester.pump();

      expect(find.byType(SnackBar), findsOneWidget);
      expect(
          find.text(
              'خطأ في الاتصال بالإنترنت. يرجى التحقق من اتصالك والمحاولة مرة أخرى.'),
          findsOneWidget);
    });
  });

  group('AppEmptyStateWidget', () {
    testWidgets('should render empty state with message', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const Scaffold(
            body: AppEmptyStateWidget(
              message: 'لا توجد عناصر',
            ),
          ),
        ),
      );

      expect(find.text('لا توجد عناصر'), findsOneWidget);
      expect(find.byIcon(Icons.inbox_outlined), findsOneWidget);
    });

    testWidgets('should show action button when provided', (tester) async {
      bool actionPressed = false;

      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: Scaffold(
            body: AppEmptyStateWidget(
              message: 'لا توجد عناصر',
              actionLabel: 'إضافة عنصر',
              onAction: () => actionPressed = true,
            ),
          ),
        ),
      );

      expect(find.text('إضافة عنصر'), findsOneWidget);

      await tester.tap(find.text('إضافة عنصر'));
      expect(actionPressed, isTrue);
    });

    testWidgets('should use custom icon when provided', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: const Scaffold(
            body: AppEmptyStateWidget(
              message: 'لا توجد منتجات',
              icon: Icons.shopping_cart_outlined,
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.shopping_cart_outlined), findsOneWidget);
    });
  });
}
