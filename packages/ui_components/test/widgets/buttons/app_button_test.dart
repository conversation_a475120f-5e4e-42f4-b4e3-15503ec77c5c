import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:ui_components/ui_components.dart';

void main() {
  group('AppButton', () {
    testWidgets('should render filled button by default', (tester) async {
      bool pressed = false;

      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: Scaffold(
            body: AppButton(
              onPressed: () => pressed = true,
              child: const Text('Test Button'),
            ),
          ),
        ),
      );

      expect(find.byType(FilledButton), findsOneWidget);
      expect(find.text('Test Button'), findsOneWidget);

      await tester.tap(find.byType(AppButton));
      expect(pressed, isTrue);
    });

    testWidgets('should render different button variants', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: Scaffold(
            body: Column(
              children: [
                AppButton(
                  variant: AppButtonVariant.filled,
                  onPressed: () {},
                  child: const Text('Filled'),
                ),
                AppButton(
                  variant: AppButtonVariant.elevated,
                  onPressed: () {},
                  child: const Text('Elevated'),
                ),
                AppButton(
                  variant: AppButtonVariant.outlined,
                  onPressed: () {},
                  child: const Text('Outlined'),
                ),
                AppButton(
                  variant: AppButtonVariant.text,
                  onPressed: () {},
                  child: const Text('Text'),
                ),
              ],
            ),
          ),
        ),
      );

      expect(find.byType(FilledButton), findsOneWidget);
      expect(find.byType(ElevatedButton), findsOneWidget);
      expect(find.byType(OutlinedButton), findsOneWidget);
      expect(find.byType(TextButton), findsOneWidget);
    });

    testWidgets('should show loading indicator when isLoading is true',
        (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: Scaffold(
            body: AppButton(
              onPressed: () {},
              isLoading: true,
              child: const Text('Loading Button'),
            ),
          ),
        ),
      );

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Loading Button'), findsNothing);
    });

    testWidgets('should be disabled when isLoading is true', (tester) async {
      bool pressed = false;

      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: Scaffold(
            body: AppButton(
              onPressed: () => pressed = true,
              isLoading: true,
              child: const Text('Loading Button'),
            ),
          ),
        ),
      );

      await tester.tap(find.byType(AppButton));
      expect(pressed, isFalse);
    });

    testWidgets('should render with icon when provided', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: Scaffold(
            body: AppButton(
              onPressed: () {},
              icon: const Icon(Icons.add),
              child: const Text('Add Item'),
            ),
          ),
        ),
      );

      expect(find.byIcon(Icons.add), findsOneWidget);
      expect(find.text('Add Item'), findsOneWidget);
    });

    testWidgets('should be full width when isFullWidth is true',
        (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: Scaffold(
            body: SizedBox(
              width: 300,
              child: AppButton(
                onPressed: () {},
                isFullWidth: true,
                child: const Text('Full Width'),
              ),
            ),
          ),
        ),
      );

      final sizedBox = tester.widget<SizedBox>(find.byType(SizedBox).last);
      expect(sizedBox.width, double.infinity);
    });

    testWidgets('should apply different sizes correctly', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: Scaffold(
            body: Column(
              children: [
                AppButton(
                  size: AppButtonSize.small,
                  onPressed: () {},
                  child: const Text('Small'),
                ),
                AppButton(
                  size: AppButtonSize.medium,
                  onPressed: () {},
                  child: const Text('Medium'),
                ),
                AppButton(
                  size: AppButtonSize.large,
                  onPressed: () {},
                  child: const Text('Large'),
                ),
              ],
            ),
          ),
        ),
      );

      expect(find.text('Small'), findsOneWidget);
      expect(find.text('Medium'), findsOneWidget);
      expect(find.text('Large'), findsOneWidget);
    });
  });
}
