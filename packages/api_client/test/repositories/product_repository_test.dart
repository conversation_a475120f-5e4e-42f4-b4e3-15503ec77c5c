import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:core/models/product.dart';
import 'package:api_client/api_client.dart';

import 'product_repository_test.mocks.dart';

@GenerateMocks([SupabaseClient])
void main() {
  group('ProductRepository', () {
    late MockSupabaseClient mockClient;
    late ProductRepository repository;

    setUp(() {
      mockClient = MockSupabaseClient();
      repository = ProductRepository(mockClient);
    });

    test('should be initialized with correct table name', () {
      expect(repository.tableName, equals('products'));
      expect(repository.client, equals(mockClient));
    });

    test('should have all required methods', () {
      expect(repository.getAll, isA<Function>());
      expect(repository.getById, isA<Function>());
      expect(repository.create, isA<Function>());
      expect(repository.update, isA<Function>());
      expect(repository.delete, isA<Function>());
      expect(repository.getProducts, isA<Function>());
      expect(repository.getProductsByVendor, isA<Function>());
      expect(repository.searchProducts, isA<Function>());
      expect(repository.getProductsByCategory, isA<Function>());
      expect(repository.updateStock, isA<Function>());
      expect(repository.toggleActiveStatus, isA<Function>());
    });

    test('should have error handling methods', () {
      expect(repository.handleException, isA<Function>());
      expect(repository.executeWithErrorHandling, isA<Function>());
    });

    group('ProductFilter', () {
      test('should create filter with all parameters', () {
        const filter = ProductFilter(
          vendorId: 'vendor-1',
          category: ProductCategory.food,
          isActive: true,
          minPrice: 10.0,
          maxPrice: 100.0,
          inStockOnly: true,
          searchQuery: 'test',
        );

        expect(filter.vendorId, equals('vendor-1'));
        expect(filter.category, equals(ProductCategory.food));
        expect(filter.isActive, equals(true));
        expect(filter.minPrice, equals(10.0));
        expect(filter.maxPrice, equals(100.0));
        expect(filter.inStockOnly, equals(true));
        expect(filter.searchQuery, equals('test'));
      });

      test('should create empty filter', () {
        const filter = ProductFilter();

        expect(filter.vendorId, isNull);
        expect(filter.category, isNull);
        expect(filter.isActive, isNull);
        expect(filter.minPrice, isNull);
        expect(filter.maxPrice, isNull);
        expect(filter.inStockOnly, isNull);
        expect(filter.searchQuery, isNull);
      });
    });
  });
}
