import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:api_client/repositories/base_repository.dart';
import 'package:api_client/exceptions/api_exceptions.dart';

import 'base_repository_test.mocks.dart';

// Test implementation of BaseRepository
class TestRepository extends BaseRepository<Map<String, dynamic>> {
  TestRepository(SupabaseClient client) : super(client, 'test_table');

  @override
  Future<List<Map<String, dynamic>>> getAll() async {
    return executeWithErrorHandling(() async {
      final response = await client.from(tableName).select();
      return List<Map<String, dynamic>>.from(response);
    });
  }

  @override
  Future<Map<String, dynamic>?> getById(String id) async {
    return executeWithErrorHandling(() async {
      final response =
          await client.from(tableName).select().eq('id', id).maybeSingle();
      return response;
    });
  }

  @override
  Future<Map<String, dynamic>> create(Map<String, dynamic> entity) async {
    return executeWithErrorHandling(() async {
      final response =
          await client.from(tableName).insert(entity).select().single();
      return response;
    });
  }

  @override
  Future<Map<String, dynamic>> update(Map<String, dynamic> entity) async {
    return executeWithErrorHandling(() async {
      final response = await client
          .from(tableName)
          .update(entity)
          .eq('id', entity['id'])
          .select()
          .single();
      return response;
    });
  }

  @override
  Future<void> delete(String id) async {
    return executeWithErrorHandling(() async {
      await client.from(tableName).delete().eq('id', id);
    });
  }
}

@GenerateMocks([SupabaseClient])
void main() {
  group('BaseRepository', () {
    late MockSupabaseClient mockClient;
    late TestRepository repository;

    setUp(() {
      mockClient = MockSupabaseClient();
      repository = TestRepository(mockClient);
    });

    test('should be initialized with correct client and table name', () {
      expect(repository.client, equals(mockClient));
      expect(repository.tableName, equals('test_table'));
    });

    test('should have all required abstract methods implemented', () {
      expect(repository.getAll, isA<Function>());
      expect(repository.getById, isA<Function>());
      expect(repository.create, isA<Function>());
      expect(repository.update, isA<Function>());
      expect(repository.delete, isA<Function>());
    });

    test('should have error handling methods', () {
      expect(repository.handleException, isA<Function>());
      expect(repository.executeWithErrorHandling, isA<Function>());
    });

    group('handleException', () {
      test('should throw ApiException for PostgrestException', () {
        const postgrestException =
            PostgrestException(message: 'Database error', code: '500');

        expect(
          () => repository.handleException(
              postgrestException, StackTrace.current),
          throwsA(isA<ApiException>().having(
            (e) => e.message,
            'message',
            contains('Database error'),
          )),
        );
      });

      test('should throw ApiException for AuthException', () {
        const authException = AuthException('Auth failed');

        expect(
          () => repository.handleException(authException, StackTrace.current),
          throwsA(isA<ApiException>().having(
            (e) => e.message,
            'message',
            contains('Authentication error'),
          )),
        );
      });

      test('should throw ApiException for unknown errors', () {
        const unknownError = 'Unknown error';

        expect(
          () => repository.handleException(unknownError, StackTrace.current),
          throwsA(isA<ApiException>().having(
            (e) => e.message,
            'message',
            contains('Unexpected error occurred'),
          )),
        );
      });
    });

    group('executeWithErrorHandling', () {
      test('should return result when operation succeeds', () async {
        final result = await repository.executeWithErrorHandling(() async {
          return 'success';
        });

        expect(result, equals('success'));
      });

      test('should handle exceptions and rethrow as ApiException', () async {
        expect(
          () => repository.executeWithErrorHandling(() async {
            throw const PostgrestException(message: 'Database error');
          }),
          throwsA(isA<ApiException>()),
        );
      });
    });
  });
}
