import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:core/models/order.dart';
import 'package:core/enums/enums.dart';
import 'package:api_client/api_client.dart';

import 'order_repository_test.mocks.dart';

@GenerateMocks([SupabaseClient])
void main() {
  group('OrderRepository', () {
    late MockSupabaseClient mockClient;
    late OrderRepository repository;

    setUp(() {
      mockClient = MockSupabaseClient();
      repository = OrderRepository(mockClient);
    });

    test('should be initialized with correct table name', () {
      expect(repository.tableName, equals('orders'));
      expect(repository.client, equals(mockClient));
    });

    test('should have all required methods', () {
      expect(repository.getAll, isA<Function>());
      expect(repository.getById, isA<Function>());
      expect(repository.create, isA<Function>());
      expect(repository.update, isA<Function>());
      expect(repository.delete, isA<Function>());
      expect(repository.getOrders, isA<Function>());
      expect(repository.getUserOrders, isA<Function>());
      expect(repository.watchUserOrders, isA<Function>());
      expect(repository.updateOrderStatus, isA<Function>());
      expect(repository.assignCourier, isA<Function>());
      expect(repository.getOrdersByStatus, isA<Function>());
      expect(repository.getPendingDeliveries, isA<Function>());
      expect(repository.getVendorOrderStats, isA<Function>());
    });

    test('should have error handling methods', () {
      expect(repository.handleException, isA<Function>());
      expect(repository.executeWithErrorHandling, isA<Function>());
    });

    group('OrderFilter', () {
      test('should create filter with all parameters', () {
        final startDate = DateTime(2024, 1, 1);
        final endDate = DateTime(2024, 12, 31);

        final filter = OrderFilter(
          customerId: 'customer-1',
          vendorId: 'vendor-1',
          courierId: 'courier-1',
          status: OrderStatus.pending,
          startDate: startDate,
          endDate: endDate,
        );

        expect(filter.customerId, equals('customer-1'));
        expect(filter.vendorId, equals('vendor-1'));
        expect(filter.courierId, equals('courier-1'));
        expect(filter.status, equals(OrderStatus.pending));
        expect(filter.startDate, equals(startDate));
        expect(filter.endDate, equals(endDate));
      });

      test('should create empty filter', () {
        const filter = OrderFilter();

        expect(filter.customerId, isNull);
        expect(filter.vendorId, isNull);
        expect(filter.courierId, isNull);
        expect(filter.status, isNull);
        expect(filter.startDate, isNull);
        expect(filter.endDate, isNull);
      });
    });
  });
}
