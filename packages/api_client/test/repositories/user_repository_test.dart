import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:supabase_flutter/supabase_flutter.dart' hide User;
import 'package:core/models/user.dart';
import 'package:core/enums/enums.dart';
import 'package:api_client/api_client.dart';

import 'user_repository_test.mocks.dart';

@GenerateMocks([SupabaseClient])
void main() {
  group('UserRepository', () {
    late MockSupabaseClient mockClient;
    late UserRepository repository;

    setUp(() {
      mockClient = MockSupabaseClient();
      repository = UserRepository(mockClient);
    });

    test('should be initialized with correct table name', () {
      expect(repository.tableName, equals('users'));
      expect(repository.client, equals(mockClient));
    });

    test('should have all required methods', () {
      expect(repository.getAll, isA<Function>());
      expect(repository.getById, isA<Function>());
      expect(repository.create, isA<Function>());
      expect(repository.update, isA<Function>());
      expect(repository.delete, isA<Function>());
      expect(repository.getUserByEmail, isA<Function>());
      expect(repository.getUsersByType, isA<Function>());
      expect(repository.searchUsers, isA<Function>());
      expect(repository.updateLastActivity, isA<Function>());
    });

    test('should have error handling methods', () {
      expect(repository.handleException, isA<Function>());
      expect(repository.executeWithErrorHandling, isA<Function>());
    });

    group('getUsersByType', () {
      test('should have correct method signature', () {
        expect(repository.getUsersByType, isA<Function>());
      });
    });

    group('searchUsers', () {
      test('should have correct method signature', () {
        expect(repository.searchUsers, isA<Function>());
      });
    });

    group('updateLastActivity', () {
      test('should have correct method signature', () {
        expect(repository.updateLastActivity, isA<Function>());
      });
    });
  });
}
