import 'package:flutter_test/flutter_test.dart';
import 'package:api_client/api_client.dart';

void main() {
  group('API Client Package', () {
    test('should export all required classes', () {
      // Test that all main classes are exported
      expect(ApiClient, isA<Type>());
      expect(BaseRepository, isA<Type>());
      expect(UserRepository, isA<Type>());
      expect(ProductRepository, isA<Type>());
      expect(OrderRepository, isA<Type>());
      expect(ApiException, isA<Type>());
      expect(NetworkException, isA<Type>());
      expect(SerializationException, isA<Type>());
    });

    test('should export filter classes', () {
      expect(ProductFilter, isA<Type>());
      expect(OrderFilter, isA<Type>());
    });

    test('should have proper inheritance hierarchy', () {
      // Test that exceptions extend ApiException
      const networkException = NetworkException('test');
      const serializationException = SerializationException('test');

      expect(networkException, isA<ApiException>());
      expect(serializationException, isA<ApiException>());
    });
  });
}
