import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:api_client/clients/supabase_client.dart';
import 'package:api_client/exceptions/api_exceptions.dart';

import 'supabase_client_test.mocks.dart';

@GenerateMocks([SupabaseClient, GoTrueClient, SupabaseStorageClient])
void main() {
  group('ApiClient', () {
    late ApiClient apiClient;

    setUp(() {
      apiClient = ApiClient();
    });

    test('should be a singleton', () {
      final instance1 = ApiClient();
      final instance2 = ApiClient();
      expect(identical(instance1, instance2), isTrue);
    });

    test('should have all required getters defined', () {
      // Test that the ApiClient class has the expected structure
      expect(apiClient, isA<ApiClient>());
      expect(apiClient.runtimeType.toString(), equals('ApiClient'));
    });

    test('should have authentication methods', () {
      expect(apiClient.signInWithEmail, isA<Function>());
      expect(apiClient.verifyOTP, isA<Function>());
      expect(apiClient.signOut, isA<Function>());
    });

    test('should have file management methods', () {
      expect(apiClient.uploadFile, isA<Function>());
      expect(apiClient.deleteFile, isA<Function>());
    });

    test('should have utility methods', () {
      expect(apiClient.executeFunction, isA<Function>());
      expect(apiClient.dispose, isA<Function>());
    });

    group('uploadFile', () {
      test('should have correct method signature', () {
        expect(apiClient.uploadFile, isA<Function>());
      });
    });

    group('deleteFile', () {
      test('should have correct method signature', () {
        expect(apiClient.deleteFile, isA<Function>());
      });
    });

    group('executeFunction', () {
      test('should have correct method signature', () {
        expect(apiClient.executeFunction, isA<Function>());
      });
    });

    group('signInWithEmail', () {
      test('should have correct method signature', () {
        expect(apiClient.signInWithEmail, isA<Function>());
      });
    });

    group('verifyOTP', () {
      test('should have correct method signature', () {
        expect(apiClient.verifyOTP, isA<Function>());
      });
    });

    test('should have dispose method', () {
      expect(() => apiClient.dispose(), returnsNormally);
    });
  });
}
