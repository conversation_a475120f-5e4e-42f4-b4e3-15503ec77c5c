import 'dart:typed_data';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../repositories/user_repository.dart';
import '../repositories/product_repository.dart';
import '../repositories/order_repository.dart';
import '../exceptions/api_exceptions.dart';

/// Main API client that provides access to all repositories
class ApiClient {
  late final SupabaseClient _client;
  late final UserRepository _userRepository;
  late final ProductRepository _productRepository;
  late final OrderRepository _orderRepository;

  /// Private constructor for singleton pattern
  ApiClient._internal();

  /// Singleton instance
  static final ApiClient _instance = ApiClient._internal();

  /// Factory constructor that returns the singleton instance
  factory ApiClient() => _instance;

  /// Initializes the API client with Supabase configuration
  Future<void> initialize({
    required String supabaseUrl,
    required String supabaseAnonKey,
    bool enableLogging = false,
  }) async {
    try {
      await Supabase.initialize(
        url: supabaseUrl,
        anonKey: supabaseAnonKey,
        debug: enableLogging,
      );

      _client = Supabase.instance.client;

      // Initialize repositories
      _userRepository = UserRepository(_client);
      _productRepository = ProductRepository(_client);
      _orderRepository = OrderRepository(_client);
    } catch (error) {
      throw ApiException(
        'Failed to initialize API client',
        originalError: error,
      );
    }
  }

  /// Returns the Supabase client instance
  SupabaseClient get client => _client;

  /// Returns the user repository
  UserRepository get users => _userRepository;

  /// Returns the product repository
  ProductRepository get products => _productRepository;

  /// Returns the order repository
  OrderRepository get orders => _orderRepository;

  /// Returns the current authenticated user
  User? get currentUser => _client.auth.currentUser;

  /// Returns the current session
  Session? get currentSession => _client.auth.currentSession;

  /// Signs in with email (magic link)
  Future<void> signInWithEmail(String email) async {
    try {
      await _client.auth.signInWithOtp(email: email);
    } catch (error) {
      throw ApiException(
        'Failed to send magic link',
        originalError: error,
      );
    }
  }

  /// Verifies the OTP token
  Future<AuthResponse> verifyOTP({
    required String email,
    required String token,
  }) async {
    try {
      return await _client.auth.verifyOTP(
        email: email,
        token: token,
        type: OtpType.email,
      );
    } catch (error) {
      throw ApiException(
        'Failed to verify OTP',
        originalError: error,
      );
    }
  }

  /// Signs out the current user
  Future<void> signOut() async {
    try {
      await _client.auth.signOut();
    } catch (error) {
      throw ApiException(
        'Failed to sign out',
        originalError: error,
      );
    }
  }

  /// Listens to authentication state changes
  Stream<AuthState> get authStateChanges => _client.auth.onAuthStateChange;

  /// Uploads a file to Supabase storage
  Future<String> uploadFile({
    required String bucket,
    required String path,
    required List<int> bytes,
    String? contentType,
  }) async {
    try {
      await _client.storage.from(bucket).uploadBinary(
            path,
            Uint8List.fromList(bytes),
            fileOptions: FileOptions(
              contentType: contentType,
            ),
          );

      return _client.storage.from(bucket).getPublicUrl(path);
    } catch (error) {
      throw ApiException(
        'Failed to upload file',
        originalError: error,
      );
    }
  }

  /// Deletes a file from Supabase storage
  Future<void> deleteFile({
    required String bucket,
    required String path,
  }) async {
    try {
      await _client.storage.from(bucket).remove([path]);
    } catch (error) {
      throw ApiException(
        'Failed to delete file',
        originalError: error,
      );
    }
  }

  /// Executes a database function
  Future<T> executeFunction<T>(
    String functionName, {
    Map<String, dynamic>? params,
  }) async {
    try {
      final response = await _client.rpc(functionName, params: params);
      return response as T;
    } catch (error) {
      throw ApiException(
        'Failed to execute function: $functionName',
        originalError: error,
      );
    }
  }

  /// Checks if the client is initialized
  bool get isInitialized {
    try {
      return Supabase.instance.client != null;
    } catch (e) {
      return false;
    }
  }

  /// Disposes of the client resources
  void dispose() {
    // Supabase client doesn't need explicit disposal
    // but we can clear references
  }
}
