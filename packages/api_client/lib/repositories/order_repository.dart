import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:core/models/order.dart';
import 'package:core/enums/enums.dart';
import 'base_repository.dart';
import '../exceptions/api_exceptions.dart';

/// Filter options for order queries
class OrderFilter {
  final String? customerId;
  final String? vendorId;
  final String? courierId;
  final OrderStatus? status;
  final DateTime? startDate;
  final DateTime? endDate;

  const OrderFilter({
    this.customerId,
    this.vendorId,
    this.courierId,
    this.status,
    this.startDate,
    this.endDate,
  });
}

/// Repository for managing order data operations
class OrderRepository extends BaseRepository<Order> {
  OrderRepository(SupabaseClient client) : super(client, 'orders');

  @override
  Future<List<Order>> getAll() async {
    return executeWithErrorHandling(() async {
      final response = await client.from(tableName).select('''
            *,
            order_items (
              id,
              order_id,
              product_id,
              product_name,
              quantity,
              unit_price,
              total_price
            )
          ''').order('created_at', ascending: false);

      return response.map((json) => _parseOrderWithItems(json)).toList();
    });
  }

  @override
  Future<Order?> getById(String id) async {
    return executeWithErrorHandling(() async {
      final response = await client.from(tableName).select('''
            *,
            order_items (
              id,
              order_id,
              product_id,
              product_name,
              quantity,
              unit_price,
              total_price
            )
          ''').eq('id', id).maybeSingle();

      return response != null ? _parseOrderWithItems(response) : null;
    });
  }

  @override
  Future<Order> create(Order entity) async {
    return executeWithErrorHandling(() async {
      final response = await client
          .from(tableName)
          .insert(entity.toJson())
          .select()
          .single();

      return Order.fromJson(response);
    });
  }

  @override
  Future<Order> update(Order entity) async {
    return executeWithErrorHandling(() async {
      final response = await client
          .from(tableName)
          .update(entity.toJson())
          .eq('id', entity.id)
          .select()
          .single();

      return Order.fromJson(response);
    });
  }

  @override
  Future<void> delete(String id) async {
    return executeWithErrorHandling(() async {
      await client.from(tableName).delete().eq('id', id);
    });
  }

  /// Retrieves orders with filtering and pagination
  Future<List<Order>> getOrders(
    OrderFilter filter, {
    int page = 0,
    int limit = 20,
  }) async {
    return executeWithErrorHandling(() async {
      var query = client.from(tableName).select('''
            *,
            order_items (
              id,
              order_id,
              product_id,
              product_name,
              quantity,
              unit_price,
              total_price
            )
          ''');

      // Apply filters
      if (filter.customerId != null) {
        query = query.eq('customer_id', filter.customerId!);
      }

      if (filter.vendorId != null) {
        query = query.eq('vendor_id', filter.vendorId!);
      }

      if (filter.courierId != null) {
        query = query.eq('courier_id', filter.courierId!);
      }

      if (filter.status != null) {
        query = query.eq('status', filter.status!.name);
      }

      if (filter.startDate != null) {
        query = query.gte('created_at', filter.startDate!.toIso8601String());
      }

      if (filter.endDate != null) {
        query = query.lte('created_at', filter.endDate!.toIso8601String());
      }

      // Apply pagination and ordering
      final response = await query
          .range(page * limit, (page + 1) * limit - 1)
          .order('created_at', ascending: false);

      return response.map((json) => _parseOrderWithItems(json)).toList();
    });
  }

  /// Retrieves orders for a specific user (customer, vendor, or courier)
  Future<List<Order>> getUserOrders(String userId, UserType userType,
      {int limit = 50}) async {
    final filter = switch (userType) {
      UserType.customer => OrderFilter(customerId: userId),
      UserType.vendor => OrderFilter(vendorId: userId),
      UserType.courier => OrderFilter(courierId: userId),
    };

    return getOrders(filter, limit: limit);
  }

  /// Watches orders for real-time updates
  Stream<List<Order>> watchUserOrders(String userId, UserType userType) {
    final column = switch (userType) {
      UserType.customer => 'customer_id',
      UserType.vendor => 'vendor_id',
      UserType.courier => 'courier_id',
    };

    return client
        .from(tableName)
        .stream(primaryKey: ['id'])
        .eq(column, userId)
        .order('created_at', ascending: false)
        .map((data) => data.map((json) => Order.fromJson(json)).toList());
  }

  /// Updates order status
  Future<Order> updateOrderStatus(String orderId, OrderStatus newStatus) async {
    return executeWithErrorHandling(() async {
      final updateData = {
        'status': newStatus.name,
        'updated_at': DateTime.now().toIso8601String(),
      };

      // If delivered, set delivered_at timestamp
      if (newStatus == OrderStatus.delivered) {
        updateData['delivered_at'] = DateTime.now().toIso8601String();
      }

      final response = await client
          .from(tableName)
          .update(updateData)
          .eq('id', orderId)
          .select('''
            *,
            order_items (
              id,
              order_id,
              product_id,
              product_name,
              quantity,
              unit_price,
              total_price
            )
          ''').single();

      return _parseOrderWithItems(response);
    });
  }

  /// Assigns a courier to an order
  Future<Order> assignCourier(String orderId, String courierId) async {
    return executeWithErrorHandling(() async {
      final response = await client
          .from(tableName)
          .update({
            'courier_id': courierId,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', orderId)
          .select('''
            *,
            order_items (
              id,
              order_id,
              product_id,
              product_name,
              quantity,
              unit_price,
              total_price
            )
          ''')
          .single();

      return _parseOrderWithItems(response);
    });
  }

  /// Retrieves orders by status
  Future<List<Order>> getOrdersByStatus(OrderStatus status,
      {int limit = 50}) async {
    return getOrders(OrderFilter(status: status), limit: limit);
  }

  /// Retrieves pending orders for couriers
  Future<List<Order>> getPendingDeliveries({int limit = 20}) async {
    return executeWithErrorHandling(() async {
      final response = await client
          .from(tableName)
          .select('''
            *,
            order_items (
              id,
              order_id,
              product_id,
              product_name,
              quantity,
              unit_price,
              total_price
            )
          ''')
          .eq('status', OrderStatus.readyForPickup.name)
          .isFilter('courier_id', null)
          .limit(limit)
          .order('created_at', ascending: true);

      return response.map((json) => _parseOrderWithItems(json)).toList();
    });
  }

  /// Gets order statistics for a vendor
  Future<Map<String, dynamic>> getVendorOrderStats(String vendorId) async {
    return executeWithErrorHandling(() async {
      final response = await client
          .rpc('get_vendor_order_stats', params: {'vendor_id': vendorId});

      return response as Map<String, dynamic>;
    });
  }

  /// Helper method to parse order with items from JSON
  Order _parseOrderWithItems(Map<String, dynamic> json) {
    try {
      // Extract order items data
      final itemsData = json['order_items'] as List<dynamic>? ?? [];
      final items = itemsData
          .map((itemJson) =>
              OrderItem.fromJson(itemJson as Map<String, dynamic>))
          .toList();

      // Remove items from main JSON to avoid conflicts
      final orderJson = Map<String, dynamic>.from(json);
      orderJson.remove('order_items');

      // Create order and add items
      final order = Order.fromJson(orderJson);
      return order.copyWith(items: items);
    } catch (error) {
      throw SerializationException(
        'Failed to parse order data',
        originalError: error,
      );
    }
  }
}
