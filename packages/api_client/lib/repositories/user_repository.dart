import 'package:supabase_flutter/supabase_flutter.dart' hide User;
import 'package:core/models/user.dart';
import 'package:core/enums/enums.dart';
import 'base_repository.dart';
import '../exceptions/api_exceptions.dart';

/// Repository for managing user data operations
class UserRepository extends BaseRepository<User> {
  UserRepository(SupabaseClient client) : super(client, 'users');

  @override
  Future<List<User>> getAll() async {
    return executeWithErrorHandling(() async {
      final response = await client
          .from(tableName)
          .select()
          .order('created_at', ascending: false);

      return response.map((json) => User.fromJson(json)).toList();
    });
  }

  @override
  Future<User?> getById(String id) async {
    return executeWithErrorHandling(() async {
      final response =
          await client.from(tableName).select().eq('id', id).maybeSingle();

      return response != null ? User.fromJson(response) : null;
    });
  }

  @override
  Future<User> create(User entity) async {
    return executeWithErrorHandling(() async {
      final response = await client
          .from(tableName)
          .insert(entity.toJson())
          .select()
          .single();

      return User.fromJson(response);
    });
  }

  @override
  Future<User> update(User entity) async {
    return executeWithErrorHandling(() async {
      final response = await client
          .from(tableName)
          .update(entity.toJson())
          .eq('id', entity.id)
          .select()
          .single();

      return User.fromJson(response);
    });
  }

  @override
  Future<void> delete(String id) async {
    return executeWithErrorHandling(() async {
      await client.from(tableName).delete().eq('id', id);
    });
  }

  /// Retrieves a user by email address
  Future<User?> getUserByEmail(String email) async {
    return executeWithErrorHandling(() async {
      final response = await client
          .from(tableName)
          .select()
          .eq('email', email)
          .maybeSingle();

      return response != null ? User.fromJson(response) : null;
    });
  }

  /// Retrieves users by type (customer, vendor, courier)
  Future<List<User>> getUsersByType(UserType userType) async {
    return executeWithErrorHandling(() async {
      final response = await client
          .from(tableName)
          .select()
          .eq('user_type', userType.name)
          .order('created_at', ascending: false);

      return response.map((json) => User.fromJson(json)).toList();
    });
  }

  /// Searches users by name or email
  Future<List<User>> searchUsers(String query, {int limit = 20}) async {
    return executeWithErrorHandling(() async {
      final response = await client
          .from(tableName)
          .select()
          .or('name.ilike.%$query%,email.ilike.%$query%')
          .limit(limit)
          .order('created_at', ascending: false);

      return response.map((json) => User.fromJson(json)).toList();
    });
  }

  /// Updates user's last activity timestamp
  Future<void> updateLastActivity(String userId) async {
    return executeWithErrorHandling(() async {
      await client.from(tableName).update(
          {'updated_at': DateTime.now().toIso8601String()}).eq('id', userId);
    });
  }
}
