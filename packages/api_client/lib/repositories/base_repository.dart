import 'package:supabase_flutter/supabase_flutter.dart';
import '../exceptions/api_exceptions.dart';

/// Base abstract class for all repositories implementing common CRUD operations
abstract class BaseRepository<T> {
  /// The Supabase client instance
  final SupabaseClient client;

  /// The table name in the database
  final String tableName;

  const BaseRepository(this.client, this.tableName);

  /// Retrieves all entities from the table
  Future<List<T>> getAll();

  /// Retrieves a single entity by its ID
  Future<T?> getById(String id);

  /// Creates a new entity in the database
  Future<T> create(T entity);

  /// Updates an existing entity in the database
  Future<T> update(T entity);

  /// Deletes an entity by its ID
  Future<void> delete(String id);

  /// Handles Supabase exceptions and converts them to app-specific exceptions
  Never handleException(Object error, StackTrace stackTrace) {
    if (error is PostgrestException) {
      throw ApiException(
        'Database error: ${error.message}',
        code: error.code,
        originalError: error,
      );
    } else if (error is AuthException) {
      throw ApiException(
        'Authentication error: ${error.message}',
        code: 'auth_error',
        originalError: error,
      );
    } else {
      throw ApiException(
        'Unexpected error occurred',
        originalError: error,
      );
    }
  }

  /// Executes a database operation with error handling
  Future<R> executeWithErrorHandling<R>(Future<R> Function() operation) async {
    try {
      return await operation();
    } catch (error, stackTrace) {
      handleException(error, stackTrace);
    }
  }
}
