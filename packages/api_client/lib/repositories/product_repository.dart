import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:core/models/product.dart';
import 'base_repository.dart';
import '../exceptions/api_exceptions.dart';

/// Filter options for product queries
class ProductFilter {
  final String? vendorId;
  final ProductCategory? category;
  final bool? isActive;
  final double? minPrice;
  final double? maxPrice;
  final bool? inStockOnly;
  final String? searchQuery;

  const ProductFilter({
    this.vendorId,
    this.category,
    this.isActive,
    this.minPrice,
    this.maxPrice,
    this.inStockOnly,
    this.searchQuery,
  });
}

/// Repository for managing product data operations
class ProductRepository extends BaseRepository<Product> {
  ProductRepository(SupabaseClient client) : super(client, 'products');

  @override
  Future<List<Product>> getAll() async {
    return executeWithErrorHandling(() async {
      final response = await client.from(tableName).select('''
            *,
            product_images (
              id,
              url,
              blur_hash,
              width,
              height,
              sort_order
            )
          ''').eq('is_active', true).order('created_at', ascending: false);

      return response.map((json) => _parseProductWithImages(json)).toList();
    });
  }

  @override
  Future<Product?> getById(String id) async {
    return executeWithErrorHandling(() async {
      final response = await client.from(tableName).select('''
            *,
            product_images (
              id,
              url,
              blur_hash,
              width,
              height,
              sort_order
            )
          ''').eq('id', id).maybeSingle();

      return response != null ? _parseProductWithImages(response) : null;
    });
  }

  @override
  Future<Product> create(Product entity) async {
    return executeWithErrorHandling(() async {
      final response = await client
          .from(tableName)
          .insert(entity.toJson())
          .select()
          .single();

      return Product.fromJson(response);
    });
  }

  @override
  Future<Product> update(Product entity) async {
    return executeWithErrorHandling(() async {
      final response = await client
          .from(tableName)
          .update(entity.toJson())
          .eq('id', entity.id)
          .select()
          .single();

      return Product.fromJson(response);
    });
  }

  @override
  Future<void> delete(String id) async {
    return executeWithErrorHandling(() async {
      await client.from(tableName).delete().eq('id', id);
    });
  }

  /// Retrieves products with filtering and pagination
  Future<List<Product>> getProducts(
    ProductFilter filter, {
    int page = 0,
    int limit = 20,
  }) async {
    return executeWithErrorHandling(() async {
      var query = client.from(tableName).select('''
            *,
            product_images (
              id,
              url,
              blur_hash,
              width,
              height,
              sort_order
            )
          ''');

      // Apply filters
      if (filter.vendorId != null) {
        query = query.eq('vendor_id', filter.vendorId!);
      }

      if (filter.category != null) {
        query = query.eq('category', filter.category!.name);
      }

      if (filter.isActive != null) {
        query = query.eq('is_active', filter.isActive!);
      }

      if (filter.minPrice != null) {
        query = query.gte('price', filter.minPrice!);
      }

      if (filter.maxPrice != null) {
        query = query.lte('price', filter.maxPrice!);
      }

      if (filter.inStockOnly == true) {
        query = query.gt('stock_quantity', 0);
      }

      if (filter.searchQuery != null && filter.searchQuery!.isNotEmpty) {
        query = query.ilike('name_ar', '%${filter.searchQuery}%');
      }

      // Apply pagination and ordering
      final response = await query
          .range(page * limit, (page + 1) * limit - 1)
          .order('created_at', ascending: false);

      return response.map((json) => _parseProductWithImages(json)).toList();
    });
  }

  /// Retrieves products by vendor ID
  Future<List<Product>> getProductsByVendor(String vendorId,
      {int limit = 50}) async {
    return getProducts(
      ProductFilter(vendorId: vendorId, isActive: true),
      limit: limit,
    );
  }

  /// Searches products by name
  Future<List<Product>> searchProducts(String query,
      {int page = 0, int limit = 20}) async {
    return getProducts(
      ProductFilter(searchQuery: query, isActive: true),
      page: page,
      limit: limit,
    );
  }

  /// Retrieves products by category
  Future<List<Product>> getProductsByCategory(
    ProductCategory category, {
    int page = 0,
    int limit = 20,
  }) async {
    return getProducts(
      ProductFilter(category: category, isActive: true),
      page: page,
      limit: limit,
    );
  }

  /// Updates product stock quantity
  Future<Product> updateStock(String productId, int newQuantity) async {
    return executeWithErrorHandling(() async {
      final response = await client
          .from(tableName)
          .update({
            'stock_quantity': newQuantity,
            'updated_at': DateTime.now().toIso8601String()
          })
          .eq('id', productId)
          .select('''
            *,
            product_images (
              id,
              url,
              blur_hash,
              width,
              height,
              sort_order
            )
          ''')
          .single();

      return _parseProductWithImages(response);
    });
  }

  /// Toggles product active status
  Future<Product> toggleActiveStatus(String productId) async {
    return executeWithErrorHandling(() async {
      // First get current status
      final current = await getById(productId);
      if (current == null) {
        throw const ApiException('Product not found');
      }

      final response = await client
          .from(tableName)
          .update({
            'is_active': !current.isActive,
            'updated_at': DateTime.now().toIso8601String()
          })
          .eq('id', productId)
          .select('''
            *,
            product_images (
              id,
              url,
              blur_hash,
              width,
              height,
              sort_order
            )
          ''')
          .single();

      return _parseProductWithImages(response);
    });
  }

  /// Helper method to parse product with images from JSON
  Product _parseProductWithImages(Map<String, dynamic> json) {
    try {
      // Extract images data
      final imagesData = json['product_images'] as List<dynamic>? ?? [];
      final images = imagesData
          .map((imageJson) =>
              ProductImage.fromJson(imageJson as Map<String, dynamic>))
          .toList();

      // Remove images from main JSON to avoid conflicts
      final productJson = Map<String, dynamic>.from(json);
      productJson.remove('product_images');

      // Create product and add images
      final product = Product.fromJson(productJson);
      return product.copyWith(images: images);
    } catch (error) {
      throw SerializationException(
        'Failed to parse product data',
        originalError: error,
      );
    }
  }
}
