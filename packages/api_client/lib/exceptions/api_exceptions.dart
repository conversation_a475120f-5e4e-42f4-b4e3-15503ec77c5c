import 'package:core/exceptions/app_exceptions.dart';

/// Exception thrown when API operations fail
class ApiException extends AppException {
  const ApiException(super.message, {super.code, super.originalError});

  @override
  String toString() => 'ApiException: $message';
}

/// Exception thrown when network connectivity issues occur
class NetworkException extends ApiException {
  const NetworkException(super.message, {super.code, super.originalError});

  @override
  String toString() => 'NetworkException: $message';
}

/// Exception thrown when data serialization/deserialization fails
class SerializationException extends ApiException {
  const SerializationException(super.message,
      {super.code, super.originalError});

  @override
  String toString() => 'SerializationException: $message';
}
