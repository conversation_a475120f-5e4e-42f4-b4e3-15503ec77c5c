import 'package:json_annotation/json_annotation.dart';

/// Enum representing different types of users in the platform
@JsonEnum()
enum UserType {
  @JsonValue('customer')
  customer,

  @JsonValue('vendor')
  vendor,

  @JsonValue('courier')
  courier;

  /// Returns the display name for the user type in Arabic
  String get displayNameAr {
    switch (this) {
      case UserType.customer:
        return 'عميل';
      case UserType.vendor:
        return 'بائع';
      case UserType.courier:
        return 'مندوب توصيل';
    }
  }

  /// Returns the user type from string value
  static UserType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'customer':
        return UserType.customer;
      case 'vendor':
        return UserType.vendor;
      case 'courier':
        return UserType.courier;
      default:
        throw ArgumentError('Invalid user type: $value');
    }
  }
}
