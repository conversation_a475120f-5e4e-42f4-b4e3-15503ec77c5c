import 'package:json_annotation/json_annotation.dart';

/// Enum representing payment methods (currently only cash on delivery)
@JsonEnum()
enum PaymentMethod {
  @JsonValue('cash_on_delivery')
  cashOnDelivery;

  /// Returns the display name for the payment method in Arabic
  String get displayNameAr {
    switch (this) {
      case PaymentMethod.cashOnDelivery:
        return 'الدفع عند الاستلام';
    }
  }

  /// Returns the payment method from string value
  static PaymentMethod fromString(String value) {
    switch (value.toLowerCase()) {
      case 'cash_on_delivery':
        return PaymentMethod.cashOnDelivery;
      default:
        throw ArgumentError('Invalid payment method: $value');
    }
  }
}
