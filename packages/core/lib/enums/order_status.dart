import 'package:json_annotation/json_annotation.dart';

/// Enum representing different order statuses in the platform
@JsonEnum()
enum OrderStatus {
  @JsonValue('pending')
  pending,

  @JsonValue('confirmed')
  confirmed,

  @JsonValue('preparing')
  preparing,

  @JsonValue('ready_for_pickup')
  readyForPickup,

  @JsonValue('picked_up')
  pickedUp,

  @JsonValue('in_transit')
  inTransit,

  @JsonValue('delivered')
  delivered,

  @JsonValue('cancelled')
  cancelled;

  /// Returns the display name for the order status in Arabic
  String get displayNameAr {
    switch (this) {
      case OrderStatus.pending:
        return 'في الانتظار';
      case OrderStatus.confirmed:
        return 'مؤكد';
      case OrderStatus.preparing:
        return 'قيد التحضير';
      case OrderStatus.readyForPickup:
        return 'جاهز للاستلام';
      case OrderStatus.pickedUp:
        return 'تم الاستلام';
      case OrderStatus.inTransit:
        return 'في الطريق';
      case OrderStatus.delivered:
        return 'تم التوصيل';
      case OrderStatus.cancelled:
        return 'ملغي';
    }
  }

  /// Returns true if the order can be cancelled
  bool get canBeCancelled {
    return this == OrderStatus.pending ||
        this == OrderStatus.confirmed ||
        this == OrderStatus.preparing;
  }

  /// Returns true if the order is in a final state
  bool get isFinal {
    return this == OrderStatus.delivered || this == OrderStatus.cancelled;
  }

  /// Returns the order status from string value
  static OrderStatus fromString(String value) {
    switch (value.toLowerCase()) {
      case 'pending':
        return OrderStatus.pending;
      case 'confirmed':
        return OrderStatus.confirmed;
      case 'preparing':
        return OrderStatus.preparing;
      case 'ready_for_pickup':
        return OrderStatus.readyForPickup;
      case 'picked_up':
        return OrderStatus.pickedUp;
      case 'in_transit':
        return OrderStatus.inTransit;
      case 'delivered':
        return OrderStatus.delivered;
      case 'cancelled':
        return OrderStatus.cancelled;
      default:
        throw ArgumentError('Invalid order status: $value');
    }
  }
}
