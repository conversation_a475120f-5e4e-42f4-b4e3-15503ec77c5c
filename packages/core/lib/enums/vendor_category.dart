import 'package:json_annotation/json_annotation.dart';

/// Enum representing different vendor categories in the platform
@JsonEnum()
enum VendorCategory {
  @JsonValue('food_beverages')
  foodBeverages,

  @JsonValue('electronics')
  electronics,

  @JsonValue('clothing_fashion')
  clothingFashion,

  @JsonValue('home_garden')
  homeGarden,

  @JsonValue('health_beauty')
  healthBeauty,

  @JsonValue('books_media')
  booksMedia,

  @JsonValue('sports_outdoors')
  sportsOutdoors,

  @JsonValue('automotive')
  automotive,

  @JsonValue('toys_games')
  toysGames,

  @JsonValue('services')
  services;

  /// Returns the display name for the vendor category in Arabic
  String get displayNameAr {
    switch (this) {
      case VendorCategory.foodBeverages:
        return 'الطعام والمشروبات';
      case VendorCategory.electronics:
        return 'الإلكترونيات';
      case VendorCategory.clothingFashion:
        return 'الملابس والأزياء';
      case VendorCategory.homeGarden:
        return 'المنزل والحديقة';
      case VendorCategory.healthBeauty:
        return 'الصحة والجمال';
      case VendorCategory.booksMedia:
        return 'الكتب والوسائط';
      case VendorCategory.sportsOutdoors:
        return 'الرياضة والأنشطة الخارجية';
      case VendorCategory.automotive:
        return 'السيارات';
      case VendorCategory.toysGames:
        return 'الألعاب والترفيه';
      case VendorCategory.services:
        return 'الخدمات';
    }
  }

  /// Returns the icon name for the vendor category
  String get iconName {
    switch (this) {
      case VendorCategory.foodBeverages:
        return 'restaurant';
      case VendorCategory.electronics:
        return 'devices';
      case VendorCategory.clothingFashion:
        return 'checkroom';
      case VendorCategory.homeGarden:
        return 'home';
      case VendorCategory.healthBeauty:
        return 'spa';
      case VendorCategory.booksMedia:
        return 'menu_book';
      case VendorCategory.sportsOutdoors:
        return 'sports_soccer';
      case VendorCategory.automotive:
        return 'directions_car';
      case VendorCategory.toysGames:
        return 'toys';
      case VendorCategory.services:
        return 'build';
    }
  }

  /// Returns the vendor category from string value
  static VendorCategory fromString(String value) {
    switch (value.toLowerCase()) {
      case 'food_beverages':
        return VendorCategory.foodBeverages;
      case 'electronics':
        return VendorCategory.electronics;
      case 'clothing_fashion':
        return VendorCategory.clothingFashion;
      case 'home_garden':
        return VendorCategory.homeGarden;
      case 'health_beauty':
        return VendorCategory.healthBeauty;
      case 'books_media':
        return VendorCategory.booksMedia;
      case 'sports_outdoors':
        return VendorCategory.sportsOutdoors;
      case 'automotive':
        return VendorCategory.automotive;
      case 'toys_games':
        return VendorCategory.toysGames;
      case 'services':
        return VendorCategory.services;
      default:
        throw ArgumentError('Invalid vendor category: $value');
    }
  }
}
