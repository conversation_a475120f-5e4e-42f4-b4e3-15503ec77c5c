import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'generated/core_localizations.dart';

/// Custom LocalizationsDelegate that provides core localization functionality
/// for all apps in the multi-vendor ecommerce platform
class AppLocalizationsDelegate
    extends LocalizationsDelegate<CoreLocalizations> {
  const AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    // Support only Arabic locale as per requirements
    return locale.languageCode == 'ar';
  }

  @override
  Future<CoreLocalizations> load(Locale locale) {
    return SynchronousFuture<CoreLocalizations>(
      lookupCoreLocalizations(locale),
    );
  }

  @override
  bool shouldReload(AppLocalizationsDelegate old) => false;

  /// Static instance for easy access
  static const AppLocalizationsDelegate delegate = AppLocalizationsDelegate();

  /// List of all localization delegates needed for the apps
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = [
    AppLocalizationsDelegate.delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
  ];

  /// Supported locales for the platform
  static const List<Locale> supportedLocales = [
    Locale('ar'), // Arabic
  ];
}

/// Extension to easily access CoreLocalizations from BuildContext
extension CoreLocalizationsExtension on BuildContext {
  CoreLocalizations get coreL10n => CoreLocalizations.of(this)!;
}
