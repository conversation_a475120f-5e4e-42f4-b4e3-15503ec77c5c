{"@@locale": "ar", "yes": "نعم", "@yes": {"description": "Yes confirmation"}, "no": "لا", "@no": {"description": "No confirmation"}, "ok": "موافق", "@ok": {"description": "OK button"}, "cancel": "إلغاء", "@cancel": {"description": "Cancel button"}, "save": "<PERSON><PERSON><PERSON>", "@save": {"description": "Save button"}, "delete": "<PERSON><PERSON><PERSON>", "@delete": {"description": "Delete button"}, "edit": "تعديل", "@edit": {"description": "Edit button"}, "add": "إضافة", "@add": {"description": "Add button"}, "search": "البحث", "@search": {"description": "Search functionality"}, "loading": "جاري التحميل...", "@loading": {"description": "Loading indicator text"}, "error": "خطأ", "@error": {"description": "Generic error text"}, "retry": "إعادة المحاولة", "@retry": {"description": "Retry button text"}, "success": "نجح", "@success": {"description": "Success message"}, "warning": "تحذير", "@warning": {"description": "Warning message"}, "info": "معلومات", "@info": {"description": "Info message"}, "name": "الاسم", "@name": {"description": "Name field"}, "email": "الب<PERSON>يد الإلكتروني", "@email": {"description": "Email field"}, "phone": "الهاتف", "@phone": {"description": "Phone field"}, "address": "العنوان", "@address": {"description": "Address field"}, "price": "السعر", "@price": {"description": "Price field"}, "quantity": "الكمية", "@quantity": {"description": "Quantity field"}, "total": "المجموع", "@total": {"description": "Total amount"}, "date": "التاريخ", "@date": {"description": "Date field"}, "time": "الوقت", "@time": {"description": "Time field"}, "status": "الحالة", "@status": {"description": "Status field"}, "description": "الوصف", "@description": {"description": "Description field"}, "category": "الفئة", "@category": {"description": "Category field"}, "image": "الصورة", "@image": {"description": "Image field"}, "required": "مطلوب", "@required": {"description": "Required field indicator"}, "optional": "اختياري", "@optional": {"description": "Optional field indicator"}, "networkError": "خطأ في الاتصال بالشبكة", "@networkError": {"description": "Network connection error"}, "serverError": "خطأ في الخادم", "@serverError": {"description": "Server error"}, "validationError": "خطأ في التحقق من البيانات", "@validationError": {"description": "Validation error"}, "authError": "خطأ في المصادقة", "@authError": {"description": "Authentication error"}, "permissionDenied": "تم رفض الإذن", "@permissionDenied": {"description": "Permission denied error"}, "notFound": "<PERSON>ير موجود", "@notFound": {"description": "Not found error"}, "timeout": "انتهت مهلة الاتصال", "@timeout": {"description": "Timeout error"}}