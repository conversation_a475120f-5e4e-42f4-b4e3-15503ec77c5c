import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'core_localizations_ar.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of CoreLocalizations
/// returned by `CoreLocalizations.of(context)`.
///
/// Applications need to include `CoreLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'generated/core_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: CoreLocalizations.localizationsDelegates,
///   supportedLocales: CoreLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the CoreLocalizations.supportedLocales
/// property.
abstract class CoreLocalizations {
  CoreLocalizations(String locale)
      : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static CoreLocalizations of(BuildContext context) {
    return Localizations.of<CoreLocalizations>(context, CoreLocalizations)!;
  }

  static const LocalizationsDelegate<CoreLocalizations> delegate =
      _CoreLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[Locale('ar')];

  /// Yes confirmation
  ///
  /// In ar, this message translates to:
  /// **'نعم'**
  String get yes;

  /// No confirmation
  ///
  /// In ar, this message translates to:
  /// **'لا'**
  String get no;

  /// OK button
  ///
  /// In ar, this message translates to:
  /// **'موافق'**
  String get ok;

  /// Cancel button
  ///
  /// In ar, this message translates to:
  /// **'إلغاء'**
  String get cancel;

  /// Save button
  ///
  /// In ar, this message translates to:
  /// **'حفظ'**
  String get save;

  /// Delete button
  ///
  /// In ar, this message translates to:
  /// **'حذف'**
  String get delete;

  /// Edit button
  ///
  /// In ar, this message translates to:
  /// **'تعديل'**
  String get edit;

  /// Add button
  ///
  /// In ar, this message translates to:
  /// **'إضافة'**
  String get add;

  /// Search functionality
  ///
  /// In ar, this message translates to:
  /// **'البحث'**
  String get search;

  /// Loading indicator text
  ///
  /// In ar, this message translates to:
  /// **'جاري التحميل...'**
  String get loading;

  /// Generic error text
  ///
  /// In ar, this message translates to:
  /// **'خطأ'**
  String get error;

  /// Retry button text
  ///
  /// In ar, this message translates to:
  /// **'إعادة المحاولة'**
  String get retry;

  /// Success message
  ///
  /// In ar, this message translates to:
  /// **'نجح'**
  String get success;

  /// Warning message
  ///
  /// In ar, this message translates to:
  /// **'تحذير'**
  String get warning;

  /// Info message
  ///
  /// In ar, this message translates to:
  /// **'معلومات'**
  String get info;

  /// Name field
  ///
  /// In ar, this message translates to:
  /// **'الاسم'**
  String get name;

  /// Email field
  ///
  /// In ar, this message translates to:
  /// **'البريد الإلكتروني'**
  String get email;

  /// Phone field
  ///
  /// In ar, this message translates to:
  /// **'الهاتف'**
  String get phone;

  /// Address field
  ///
  /// In ar, this message translates to:
  /// **'العنوان'**
  String get address;

  /// Price field
  ///
  /// In ar, this message translates to:
  /// **'السعر'**
  String get price;

  /// Quantity field
  ///
  /// In ar, this message translates to:
  /// **'الكمية'**
  String get quantity;

  /// Total amount
  ///
  /// In ar, this message translates to:
  /// **'المجموع'**
  String get total;

  /// Date field
  ///
  /// In ar, this message translates to:
  /// **'التاريخ'**
  String get date;

  /// Time field
  ///
  /// In ar, this message translates to:
  /// **'الوقت'**
  String get time;

  /// Status field
  ///
  /// In ar, this message translates to:
  /// **'الحالة'**
  String get status;

  /// Description field
  ///
  /// In ar, this message translates to:
  /// **'الوصف'**
  String get description;

  /// Category field
  ///
  /// In ar, this message translates to:
  /// **'الفئة'**
  String get category;

  /// Image field
  ///
  /// In ar, this message translates to:
  /// **'الصورة'**
  String get image;

  /// Required field indicator
  ///
  /// In ar, this message translates to:
  /// **'مطلوب'**
  String get required;

  /// Optional field indicator
  ///
  /// In ar, this message translates to:
  /// **'اختياري'**
  String get optional;

  /// Network connection error
  ///
  /// In ar, this message translates to:
  /// **'خطأ في الاتصال بالشبكة'**
  String get networkError;

  /// Server error
  ///
  /// In ar, this message translates to:
  /// **'خطأ في الخادم'**
  String get serverError;

  /// Validation error
  ///
  /// In ar, this message translates to:
  /// **'خطأ في التحقق من البيانات'**
  String get validationError;

  /// Authentication error
  ///
  /// In ar, this message translates to:
  /// **'خطأ في المصادقة'**
  String get authError;

  /// Permission denied error
  ///
  /// In ar, this message translates to:
  /// **'تم رفض الإذن'**
  String get permissionDenied;

  /// Not found error
  ///
  /// In ar, this message translates to:
  /// **'غير موجود'**
  String get notFound;

  /// Timeout error
  ///
  /// In ar, this message translates to:
  /// **'انتهت مهلة الاتصال'**
  String get timeout;
}

class _CoreLocalizationsDelegate
    extends LocalizationsDelegate<CoreLocalizations> {
  const _CoreLocalizationsDelegate();

  @override
  Future<CoreLocalizations> load(Locale locale) {
    return SynchronousFuture<CoreLocalizations>(
        lookupCoreLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['ar'].contains(locale.languageCode);

  @override
  bool shouldReload(_CoreLocalizationsDelegate old) => false;
}

CoreLocalizations lookupCoreLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar':
      return CoreLocalizationsAr();
  }

  throw FlutterError(
      'CoreLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
      'an issue with the localizations generation tool. Please file an issue '
      'on GitHub with a reproducible sample app and the gen-l10n configuration '
      'that was used.');
}
