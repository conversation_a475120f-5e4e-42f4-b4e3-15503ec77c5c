// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'core_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class CoreLocalizationsAr extends CoreLocalizations {
  CoreLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get yes => 'نعم';

  @override
  String get no => 'لا';

  @override
  String get ok => 'موافق';

  @override
  String get cancel => 'إلغاء';

  @override
  String get save => 'حفظ';

  @override
  String get delete => 'حذف';

  @override
  String get edit => 'تعديل';

  @override
  String get add => 'إضافة';

  @override
  String get search => 'البحث';

  @override
  String get loading => 'جاري التحميل...';

  @override
  String get error => 'خطأ';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get success => 'نجح';

  @override
  String get warning => 'تحذير';

  @override
  String get info => 'معلومات';

  @override
  String get name => 'الاسم';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get phone => 'الهاتف';

  @override
  String get address => 'العنوان';

  @override
  String get price => 'السعر';

  @override
  String get quantity => 'الكمية';

  @override
  String get total => 'المجموع';

  @override
  String get date => 'التاريخ';

  @override
  String get time => 'الوقت';

  @override
  String get status => 'الحالة';

  @override
  String get description => 'الوصف';

  @override
  String get category => 'الفئة';

  @override
  String get image => 'الصورة';

  @override
  String get required => 'مطلوب';

  @override
  String get optional => 'اختياري';

  @override
  String get networkError => 'خطأ في الاتصال بالشبكة';

  @override
  String get serverError => 'خطأ في الخادم';

  @override
  String get validationError => 'خطأ في التحقق من البيانات';

  @override
  String get authError => 'خطأ في المصادقة';

  @override
  String get permissionDenied => 'تم رفض الإذن';

  @override
  String get notFound => 'غير موجود';

  @override
  String get timeout => 'انتهت مهلة الاتصال';
}
