/// Application-wide constants
class AppConstants {
  AppConstants._();

  // App Information
  static const String appName = 'متجر متعدد البائعين';
  static const String appVersion = '1.0.0';

  // API Configuration
  static const int apiTimeoutSeconds = 30;
  static const int maxRetryAttempts = 3;

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // Image Configuration
  static const int maxImageSizeBytes = 5 * 1024 * 1024; // 5MB
  static const List<String> supportedImageFormats = [
    'jpg',
    'jpeg',
    'png',
    'webp'
  ];
  static const int thumbnailSize = 200;
  static const int maxImagesPerProduct = 10;

  // Validation Limits
  static const int minPasswordLength = 8;
  static const int maxNameLength = 100;
  static const int maxDescriptionLength = 1000;
  static const int maxAddressLength = 200;
  static const int minPhoneLength = 10;
  static const int maxPhoneLength = 15;

  // Business Rules
  static const double minOrderAmount = 10.0;
  static const double maxOrderAmount = 10000.0;
  static const int maxItemsPerOrder = 50;
  static const double deliveryFee = 15.0;
  static const double freeDeliveryThreshold = 100.0;

  // Rating System
  static const double minRating = 1.0;
  static const double maxRating = 5.0;
  static const int minRatingsForDisplay = 5;

  // Cache Configuration
  static const Duration cacheExpiration = Duration(hours: 1);
  static const Duration imageCacheExpiration = Duration(days: 7);

  // Location
  static const double defaultLatitude = 24.7136;
  static const double defaultLongitude = 46.6753;
  static const String defaultCountry = 'Saudi Arabia';
  static const String defaultCurrency = 'SAR';
  static const String currencySymbol = 'ر.س';

  // Date Formats
  static const String dateFormat = 'yyyy-MM-dd';
  static const String timeFormat = 'HH:mm';
  static const String dateTimeFormat = 'yyyy-MM-dd HH:mm';
  static const String displayDateFormat = 'dd/MM/yyyy';
  static const String displayTimeFormat = 'hh:mm a';

  // Regular Expressions
  static const String emailRegex =
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static const String phoneRegex = r'^\+?[0-9]\d{8,14}$';
  static const String arabicTextRegex = r'^[\u0600-\u06FF\s\d\p{P}]+$';

  // Error Messages (Keys for localization)
  static const String errorGeneric = 'error_generic';
  static const String errorNetwork = 'error_network';
  static const String errorTimeout = 'error_timeout';
  static const String errorUnauthorized = 'error_unauthorized';
  static const String errorNotFound = 'error_not_found';
  static const String errorValidation = 'error_validation';

  // Success Messages (Keys for localization)
  static const String successGeneric = 'success_generic';
  static const String successSaved = 'success_saved';
  static const String successDeleted = 'success_deleted';
  static const String successUpdated = 'success_updated';

  // Storage Keys
  static const String keyAuthToken = 'auth_token';
  static const String keyUserId = 'user_id';
  static const String keyUserType = 'user_type';
  static const String keyLanguage = 'language';
  static const String keyTheme = 'theme';
  static const String keyNotifications = 'notifications_enabled';
  static const String keyLocationPermission = 'location_permission';

  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);

  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double defaultBorderRadius = 8.0;
  static const double cardElevation = 2.0;
  static const double buttonHeight = 48.0;
  static const double inputHeight = 56.0;

  // Notification Types
  static const String notificationOrderUpdate = 'order_update';
  static const String notificationNewOrder = 'new_order';
  static const String notificationDeliveryAssignment = 'delivery_assignment';
  static const String notificationPromotion = 'promotion';
}
