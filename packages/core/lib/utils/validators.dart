import '../constants/app_constants.dart';

/// Utility class for input validation
class Validators {
  Validators._();

  /// Validates email address
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'البريد الإلكتروني مطلوب';
    }

    final emailRegExp = RegExp(AppConstants.emailRegex);
    if (!emailRegExp.hasMatch(value)) {
      return 'البريد الإلكتروني غير صحيح';
    }

    return null;
  }

  /// Validates phone number
  static String? validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return 'رقم الهاتف مطلوب';
    }

    // Remove spaces and special characters except +
    final cleanPhone = value.replaceAll(RegExp(r'[^\d+]'), '');

    if (cleanPhone.length < AppConstants.minPhoneLength ||
        cleanPhone.length > AppConstants.maxPhoneLength) {
      return 'رقم الهاتف يجب أن يكون بين ${AppConstants.minPhoneLength} و ${AppConstants.maxPhoneLength} أرقام';
    }

    final phoneRegExp = RegExp(AppConstants.phoneRegex);
    if (!phoneRegExp.hasMatch(cleanPhone)) {
      return 'رقم الهاتف غير صحيح';
    }

    return null;
  }

  /// Validates required field
  static String? validateRequired(String? value, {String fieldName = 'الحقل'}) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName مطلوب';
    }
    return null;
  }

  /// Validates name (user name, business name, etc.)
  static String? validateName(String? value, {String fieldName = 'الاسم'}) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName مطلوب';
    }

    if (value.trim().length > AppConstants.maxNameLength) {
      return '$fieldName يجب أن يكون أقل من ${AppConstants.maxNameLength} حرف';
    }

    // Check if contains at least one Arabic character or letter
    if (!RegExp(r'[\u0600-\u06FF\p{L}]', unicode: true).hasMatch(value)) {
      return '$fieldName يجب أن يحتوي على أحرف صحيحة';
    }

    return null;
  }

  /// Validates description
  static String? validateDescription(String? value, {bool required = false}) {
    if (required && (value == null || value.trim().isEmpty)) {
      return 'الوصف مطلوب';
    }

    if (value != null && value.length > AppConstants.maxDescriptionLength) {
      return 'الوصف يجب أن يكون أقل من ${AppConstants.maxDescriptionLength} حرف';
    }

    return null;
  }

  /// Validates price
  static String? validatePrice(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'السعر مطلوب';
    }

    final price = double.tryParse(value);
    if (price == null) {
      return 'السعر يجب أن يكون رقم صحيح';
    }

    if (price <= 0) {
      return 'السعر يجب أن يكون أكبر من صفر';
    }

    if (price > AppConstants.maxOrderAmount) {
      return 'السعر يجب أن يكون أقل من ${AppConstants.maxOrderAmount}';
    }

    return null;
  }

  /// Validates quantity
  static String? validateQuantity(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'الكمية مطلوبة';
    }

    final quantity = int.tryParse(value);
    if (quantity == null) {
      return 'الكمية يجب أن تكون رقم صحيح';
    }

    if (quantity <= 0) {
      return 'الكمية يجب أن تكون أكبر من صفر';
    }

    if (quantity > 1000) {
      return 'الكمية يجب أن تكون أقل من 1000';
    }

    return null;
  }

  /// Validates address
  static String? validateAddress(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'العنوان مطلوب';
    }

    if (value.trim().length < 10) {
      return 'العنوان يجب أن يكون أكثر تفصيلاً';
    }

    if (value.length > AppConstants.maxAddressLength) {
      return 'العنوان يجب أن يكون أقل من ${AppConstants.maxAddressLength} حرف';
    }

    return null;
  }

  /// Validates rating
  static String? validateRating(double? value) {
    if (value == null) {
      return 'التقييم مطلوب';
    }

    if (value < AppConstants.minRating || value > AppConstants.maxRating) {
      return 'التقييم يجب أن يكون بين ${AppConstants.minRating} و ${AppConstants.maxRating}';
    }

    return null;
  }

  /// Validates order amount
  static String? validateOrderAmount(double? value) {
    if (value == null) {
      return 'مبلغ الطلب مطلوب';
    }

    if (value < AppConstants.minOrderAmount) {
      return 'الحد الأدنى للطلب هو ${AppConstants.minOrderAmount} ${AppConstants.currencySymbol}';
    }

    if (value > AppConstants.maxOrderAmount) {
      return 'الحد الأقصى للطلب هو ${AppConstants.maxOrderAmount} ${AppConstants.currencySymbol}';
    }

    return null;
  }

  /// Validates image file size
  static String? validateImageSize(int sizeInBytes) {
    if (sizeInBytes > AppConstants.maxImageSizeBytes) {
      final maxSizeMB = AppConstants.maxImageSizeBytes / (1024 * 1024);
      return 'حجم الصورة يجب أن يكون أقل من ${maxSizeMB.toStringAsFixed(1)} ميجابايت';
    }

    return null;
  }

  /// Validates image format
  static String? validateImageFormat(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    if (!AppConstants.supportedImageFormats.contains(extension)) {
      return 'صيغة الصورة غير مدعومة. الصيغ المدعومة: ${AppConstants.supportedImageFormats.join(', ')}';
    }

    return null;
  }

  /// Validates Arabic text
  static String? validateArabicText(String? value,
      {String fieldName = 'النص'}) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName مطلوب';
    }

    // Check if contains Arabic characters
    if (!RegExp(r'[\u0600-\u06FF]').hasMatch(value)) {
      return '$fieldName يجب أن يحتوي على نص عربي';
    }

    return null;
  }

  /// Validates coordinates (latitude/longitude)
  static String? validateCoordinate(double? value, {required bool isLatitude}) {
    if (value == null) {
      return isLatitude ? 'خط العرض مطلوب' : 'خط الطول مطلوب';
    }

    if (isLatitude) {
      if (value < -90 || value > 90) {
        return 'خط العرض يجب أن يكون بين -90 و 90';
      }
    } else {
      if (value < -180 || value > 180) {
        return 'خط الطول يجب أن يكون بين -180 و 180';
      }
    }

    return null;
  }

  /// Combines multiple validators
  static String? combineValidators(
      String? value, List<String? Function(String?)> validators) {
    for (final validator in validators) {
      final result = validator(value);
      if (result != null) return result;
    }
    return null;
  }
}
