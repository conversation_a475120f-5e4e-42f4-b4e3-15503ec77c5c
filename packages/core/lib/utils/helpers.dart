import 'dart:async';
import 'dart:math';
import '../constants/app_constants.dart';

/// Utility helper functions
class Helpers {
  Helpers._();

  /// Generates a random string of specified length
  static String generateRandomString(int length) {
    const chars =
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random();
    return String.fromCharCodes(
      Iterable.generate(
          length, (_) => chars.codeUnitAt(random.nextInt(chars.length))),
    );
  }

  /// Generates a random ID
  static String generateId() {
    return generateRandomString(16);
  }

  /// Calculates distance between two coordinates in kilometers
  static double calculateDistance(
    double lat1,
    double lon1,
    double lat2,
    double lon2,
  ) {
    const double earthRadius = 6371; // Earth's radius in kilometers

    final double dLat = _degreesToRadians(lat2 - lat1);
    final double dLon = _degreesToRadians(lon2 - lon1);

    final double a = sin(dLat / 2) * sin(dLat / 2) +
        cos(_degreesToRadians(lat1)) *
            cos(_degreesToRadians(lat2)) *
            sin(dLon / 2) *
            sin(dLon / 2);

    final double c = 2 * atan2(sqrt(a), sqrt(1 - a));

    return earthRadius * c;
  }

  /// Converts degrees to radians
  static double _degreesToRadians(double degrees) {
    return degrees * (pi / 180);
  }

  /// Calculates delivery fee based on distance
  static double calculateDeliveryFee(double distanceKm) {
    if (distanceKm <= 5) return AppConstants.deliveryFee;
    if (distanceKm <= 10) return AppConstants.deliveryFee * 1.5;
    if (distanceKm <= 20) return AppConstants.deliveryFee * 2;
    return AppConstants.deliveryFee * 3;
  }

  /// Checks if order qualifies for free delivery
  static bool qualifiesForFreeDelivery(double orderAmount) {
    return orderAmount >= AppConstants.freeDeliveryThreshold;
  }

  /// Calculates estimated delivery time based on distance and current time
  static DateTime calculateEstimatedDeliveryTime(double distanceKm) {
    final now = DateTime.now();

    // Base preparation time: 30 minutes
    int preparationMinutes = 30;

    // Delivery time based on distance (assuming 30 km/h average speed)
    int deliveryMinutes = (distanceKm / 30 * 60).ceil();

    // Add buffer time
    int bufferMinutes = 15;

    final totalMinutes = preparationMinutes + deliveryMinutes + bufferMinutes;

    return now.add(Duration(minutes: totalMinutes));
  }

  /// Formats file size in bytes to human readable format
  static String formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024)
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// Generates a color from string (useful for avatars)
  static int generateColorFromString(String text) {
    int hash = 0;
    for (int i = 0; i < text.length; i++) {
      hash = text.codeUnitAt(i) + ((hash << 5) - hash);
    }

    // Convert to positive number and limit to valid color range
    final color = (hash.abs() % 0xFFFFFF) | 0xFF000000;
    return color;
  }

  /// Debounces function calls
  static void debounce(
    String key,
    Duration delay,
    void Function() callback,
  ) {
    _debounceTimers[key]?.cancel();
    _debounceTimers[key] = Timer(delay, callback);
  }

  static final Map<String, Timer> _debounceTimers = {};

  /// Throttles function calls
  static void throttle(
    String key,
    Duration interval,
    void Function() callback,
  ) {
    if (_throttleTimestamps[key] != null) {
      final elapsed = DateTime.now().difference(_throttleTimestamps[key]!);
      if (elapsed < interval) return;
    }

    _throttleTimestamps[key] = DateTime.now();
    callback();
  }

  static final Map<String, DateTime> _throttleTimestamps = {};

  /// Clamps a value between min and max
  static T clamp<T extends num>(T value, T min, T max) {
    if (value < min) return min;
    if (value > max) return max;
    return value;
  }

  /// Checks if a value is within a range
  static bool isInRange<T extends num>(T value, T min, T max) {
    return value >= min && value <= max;
  }

  /// Rounds a number to specified decimal places
  static double roundToDecimalPlaces(double value, int decimalPlaces) {
    final factor = pow(10, decimalPlaces).toDouble();
    return (value * factor).round() / factor;
  }

  /// Converts a list to a map grouped by a key function
  static Map<K, List<T>> groupBy<T, K>(
    Iterable<T> iterable,
    K Function(T) keyFunction,
  ) {
    final map = <K, List<T>>{};
    for (final item in iterable) {
      final key = keyFunction(item);
      map.putIfAbsent(key, () => []).add(item);
    }
    return map;
  }

  /// Safely parses JSON with error handling
  static T? safeJsonParse<T>(
    dynamic json,
    T Function(Map<String, dynamic>) fromJson,
  ) {
    try {
      if (json is Map<String, dynamic>) {
        return fromJson(json);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Safely parses a list of JSON objects
  static List<T> safeJsonParseList<T>(
    dynamic json,
    T Function(Map<String, dynamic>) fromJson,
  ) {
    try {
      if (json is List) {
        return json
            .where((item) => item is Map<String, dynamic>)
            .map((item) => fromJson(item as Map<String, dynamic>))
            .toList();
      }
      return [];
    } catch (e) {
      return [];
    }
  }

  /// Creates a delay (useful for testing and animations)
  static Future<void> delay(Duration duration) {
    return Future.delayed(duration);
  }

  /// Retries an async operation with exponential backoff
  static Future<T> retryWithBackoff<T>(
    Future<T> Function() operation, {
    int maxRetries = AppConstants.maxRetryAttempts,
    Duration initialDelay = const Duration(seconds: 1),
    double backoffMultiplier = 2.0,
  }) async {
    int attempt = 0;
    Duration delay = initialDelay;

    while (attempt < maxRetries) {
      try {
        return await operation();
      } catch (e) {
        attempt++;
        if (attempt >= maxRetries) rethrow;

        await Future.delayed(delay);
        delay = Duration(
            milliseconds: (delay.inMilliseconds * backoffMultiplier).round());
      }
    }

    throw Exception('Max retries exceeded');
  }

  /// Checks if current time is within business hours
  static bool isWithinBusinessHours({
    int startHour = 8,
    int endHour = 22,
  }) {
    final now = DateTime.now();
    final currentHour = now.hour;
    return currentHour >= startHour && currentHour < endHour;
  }

  /// Calculates business days between two dates
  static int calculateBusinessDays(DateTime start, DateTime end) {
    if (start.isAfter(end)) return 0;

    int businessDays = 0;
    DateTime current = start;

    while (current.isBefore(end) || current.isAtSameMomentAs(end)) {
      // Skip weekends (Friday = 5, Saturday = 6 in Dart)
      if (current.weekday != DateTime.friday &&
          current.weekday != DateTime.saturday) {
        businessDays++;
      }
      current = current.add(const Duration(days: 1));
    }

    return businessDays;
  }
}
