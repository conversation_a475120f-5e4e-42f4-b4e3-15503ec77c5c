import 'dart:math' show pow;
import 'package:intl/intl.dart';
import '../constants/app_constants.dart';

/// Extension methods for String
extension StringExtensions on String {
  /// Checks if string is a valid email
  bool get isValidEmail {
    return RegExp(AppConstants.emailRegex).hasMatch(this);
  }

  /// Checks if string is a valid phone number
  bool get isValidPhone {
    final cleanPhone = replaceAll(RegExp(r'[^\d+]'), '');
    return RegExp(AppConstants.phoneRegex).hasMatch(cleanPhone) &&
        cleanPhone.length >= AppConstants.minPhoneLength &&
        cleanPhone.length <= AppConstants.maxPhoneLength;
  }

  /// Checks if string contains Arabic characters
  bool get hasArabicCharacters {
    return RegExp(r'[\u0600-\u06FF]').hasMatch(this);
  }

  /// Capitalizes first letter of each word
  String get titleCase {
    return split(' ')
        .map((word) => word.isEmpty
            ? word
            : word[0].toUpperCase() + word.substring(1).toLowerCase())
        .join(' ');
  }

  /// Truncates string to specified length with ellipsis
  String truncate(int maxLength, {String ellipsis = '...'}) {
    if (length <= maxLength) return this;
    return '${substring(0, maxLength - ellipsis.length)}$ellipsis';
  }

  /// Removes extra whitespace and normalizes string
  String get normalized {
    return trim().replaceAll(RegExp(r'\s+'), ' ');
  }

  /// Converts string to double safely
  double? get toDoubleOrNull {
    return double.tryParse(this);
  }

  /// Converts string to int safely
  int? get toIntOrNull {
    return int.tryParse(this);
  }

  /// Checks if string is null or empty
  bool get isNullOrEmpty {
    return isEmpty;
  }

  /// Checks if string is not null and not empty
  bool get isNotNullOrEmpty {
    return isNotEmpty;
  }

  /// Formats phone number for display
  String get formattedPhone {
    final cleanPhone = replaceAll(RegExp(r'[^\d+]'), '');
    if (cleanPhone.startsWith('+966')) {
      // Saudi phone number
      final number = cleanPhone.substring(4);
      if (number.length == 9) {
        return '+966 ${number.substring(0, 2)} ${number.substring(2, 5)} ${number.substring(5)}';
      }
    }
    return this;
  }
}

/// Extension methods for DateTime
extension DateTimeExtensions on DateTime {
  /// Formats date for display
  String get formattedDate {
    return DateFormat(AppConstants.displayDateFormat).format(this);
  }

  /// Formats time for display
  String get formattedTime {
    return DateFormat(AppConstants.displayTimeFormat).format(this);
  }

  /// Formats date and time for display
  String get formattedDateTime {
    return '${formattedDate} ${formattedTime}';
  }

  /// Returns relative time string (e.g., "منذ ساعة", "منذ يوم")
  String get relativeTime {
    final now = DateTime.now();
    final difference = now.difference(this);

    if (difference.inDays > 365) {
      final years = (difference.inDays / 365).floor();
      return years == 1 ? 'منذ سنة' : 'منذ $years سنوات';
    } else if (difference.inDays > 30) {
      final months = (difference.inDays / 30).floor();
      return months == 1 ? 'منذ شهر' : 'منذ $months أشهر';
    } else if (difference.inDays > 0) {
      return difference.inDays == 1
          ? 'منذ يوم'
          : 'منذ ${difference.inDays} أيام';
    } else if (difference.inHours > 0) {
      return difference.inHours == 1
          ? 'منذ ساعة'
          : 'منذ ${difference.inHours} ساعات';
    } else if (difference.inMinutes > 0) {
      return difference.inMinutes == 1
          ? 'منذ دقيقة'
          : 'منذ ${difference.inMinutes} دقائق';
    } else {
      return 'الآن';
    }
  }

  /// Checks if date is today
  bool get isToday {
    final now = DateTime.now();
    return year == now.year && month == now.month && day == now.day;
  }

  /// Checks if date is yesterday
  bool get isYesterday {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return year == yesterday.year &&
        month == yesterday.month &&
        day == yesterday.day;
  }

  /// Checks if date is tomorrow
  bool get isTomorrow {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    return year == tomorrow.year &&
        month == tomorrow.month &&
        day == tomorrow.day;
  }

  /// Returns start of day (00:00:00)
  DateTime get startOfDay {
    return DateTime(year, month, day);
  }

  /// Returns end of day (23:59:59)
  DateTime get endOfDay {
    return DateTime(year, month, day, 23, 59, 59, 999);
  }
}

/// Extension methods for double
extension DoubleExtensions on double {
  /// Formats price with currency symbol
  String get formattedPrice {
    return '${toStringAsFixed(2)} ${AppConstants.currencySymbol}';
  }

  /// Formats rating with one decimal place
  String get formattedRating {
    return toStringAsFixed(1);
  }

  /// Formats percentage
  String get formattedPercentage {
    return '${toStringAsFixed(1)}%';
  }

  /// Formats weight in grams to appropriate unit
  String get formattedWeight {
    if (this >= 1000) {
      return '${(this / 1000).toStringAsFixed(1)} كيلو';
    }
    return '${toStringAsFixed(0)} جرام';
  }

  /// Rounds to specified decimal places
  double roundToDecimalPlaces(int decimalPlaces) {
    final factor = pow(10, decimalPlaces).toDouble();
    return (this * factor).round() / factor;
  }
}

/// Extension methods for int
extension IntExtensions on int {
  /// Formats quantity with Arabic text
  String get formattedQuantity {
    if (this == 1) return 'قطعة واحدة';
    if (this == 2) return 'قطعتان';
    if (this <= 10) return '$this قطع';
    return '$this قطعة';
  }

  /// Formats count with Arabic text
  String get formattedCount {
    if (this == 0) return 'لا يوجد';
    if (this == 1) return 'واحد';
    if (this == 2) return 'اثنان';
    return toString();
  }

  /// Converts bytes to human readable format
  String get formattedBytes {
    if (this < 1024) return '$this بايت';
    if (this < 1024 * 1024)
      return '${(this / 1024).toStringAsFixed(1)} كيلوبايت';
    if (this < 1024 * 1024 * 1024)
      return '${(this / (1024 * 1024)).toStringAsFixed(1)} ميجابايت';
    return '${(this / (1024 * 1024 * 1024)).toStringAsFixed(1)} جيجابايت';
  }
}

/// Extension methods for List
extension ListExtensions<T> on List<T> {
  /// Safely gets element at index, returns null if out of bounds
  T? safeElementAt(int index) {
    if (index < 0 || index >= length) return null;
    return this[index];
  }

  /// Chunks list into smaller lists of specified size
  List<List<T>> chunk(int size) {
    final chunks = <List<T>>[];
    for (int i = 0; i < length; i += size) {
      chunks.add(sublist(i, i + size > length ? length : i + size));
    }
    return chunks;
  }

  /// Removes duplicates while preserving order
  List<T> get unique {
    final seen = <T>{};
    return where((element) => seen.add(element)).toList();
  }
}

/// Extension methods for Duration
extension DurationExtensions on Duration {
  /// Formats duration in Arabic
  String get formattedDuration {
    if (inDays > 0) {
      return inDays == 1 ? 'يوم واحد' : '$inDays أيام';
    } else if (inHours > 0) {
      return inHours == 1 ? 'ساعة واحدة' : '$inHours ساعات';
    } else if (inMinutes > 0) {
      return inMinutes == 1 ? 'دقيقة واحدة' : '$inMinutes دقائق';
    } else {
      return 'أقل من دقيقة';
    }
  }
}
