import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';
import '../enums/enums.dart';

part 'product.g.dart';

/// Model representing a product image with BlurHash support
@JsonSerializable()
class ProductImage extends Equatable {
  /// URL of the image
  final String url;

  /// BlurHash string for smooth loading transitions
  final String blurHash;

  /// Image width in pixels
  final int width;

  /// Image height in pixels
  final int height;

  /// Sort order for displaying multiple images
  final int sortOrder;

  const ProductImage({
    required this.url,
    required this.blurHash,
    required this.width,
    required this.height,
    this.sortOrder = 0,
  });

  /// Creates a ProductImage from JSON
  factory ProductImage.fromJson(Map<String, dynamic> json) =>
      _$ProductImageFromJson(json);

  /// Converts ProductImage to JSON
  Map<String, dynamic> toJson() => _$ProductImageToJson(this);

  /// Returns the aspect ratio of the image
  double get aspectRatio => width / height;

  @override
  List<Object?> get props => [url, blurHash, width, height, sortOrder];
}

/// Enum for product categories
@JsonEnum()
enum ProductCategory {
  @JsonValue('food')
  food,

  @JsonValue('electronics')
  electronics,

  @JsonValue('clothing')
  clothing,

  @JsonValue('home')
  home,

  @JsonValue('beauty')
  beauty,

  @JsonValue('books')
  books,

  @JsonValue('sports')
  sports,

  @JsonValue('automotive')
  automotive,

  @JsonValue('toys')
  toys,

  @JsonValue('other')
  other;

  /// Returns the display name for the product category in Arabic
  String get displayNameAr {
    switch (this) {
      case ProductCategory.food:
        return 'طعام';
      case ProductCategory.electronics:
        return 'إلكترونيات';
      case ProductCategory.clothing:
        return 'ملابس';
      case ProductCategory.home:
        return 'منزل';
      case ProductCategory.beauty:
        return 'جمال';
      case ProductCategory.books:
        return 'كتب';
      case ProductCategory.sports:
        return 'رياضة';
      case ProductCategory.automotive:
        return 'سيارات';
      case ProductCategory.toys:
        return 'ألعاب';
      case ProductCategory.other:
        return 'أخرى';
    }
  }
}

/// Model representing a product
@JsonSerializable()
class Product extends Equatable {
  /// Unique identifier for the product
  final String id;

  /// ID of the vendor who owns this product
  final String vendorId;

  /// Product name in Arabic
  final String nameAr;

  /// Product description in Arabic
  final String? descriptionAr;

  /// Product price
  final double price;

  /// List of product images
  final List<ProductImage> images;

  /// Current stock quantity
  final int stockQuantity;

  /// Product category
  final ProductCategory category;

  /// Whether the product is currently active/available
  final bool isActive;

  /// When the product was created
  final DateTime createdAt;

  /// When the product was last updated
  final DateTime updatedAt;

  /// Product weight in grams (optional)
  final double? weightGrams;

  /// Product dimensions in cm (optional)
  final String? dimensions;

  const Product({
    required this.id,
    required this.vendorId,
    required this.nameAr,
    this.descriptionAr,
    required this.price,
    this.images = const [],
    required this.stockQuantity,
    required this.category,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
    this.weightGrams,
    this.dimensions,
  });

  /// Creates a Product from JSON
  factory Product.fromJson(Map<String, dynamic> json) =>
      _$ProductFromJson(json);

  /// Converts Product to JSON
  Map<String, dynamic> toJson() => _$ProductToJson(this);

  /// Returns the primary image (first in the list)
  ProductImage? get primaryImage {
    if (images.isEmpty) return null;
    final sortedImages = List<ProductImage>.from(images)
      ..sort((a, b) => a.sortOrder.compareTo(b.sortOrder));
    return sortedImages.first;
  }

  /// Returns true if the product is in stock
  bool get isInStock => stockQuantity > 0;

  /// Returns true if the product is available for purchase
  bool get isAvailable => isActive && isInStock;

  /// Returns the formatted price as a string
  String get formattedPrice => '${price.toStringAsFixed(2)} ر.س';

  /// Returns stock status in Arabic
  String get stockStatusAr {
    if (stockQuantity == 0) return 'نفد المخزون';
    if (stockQuantity < 5) return 'كمية محدودة';
    return 'متوفر';
  }

  /// Creates a copy of this product with updated fields
  Product copyWith({
    String? id,
    String? vendorId,
    String? nameAr,
    String? descriptionAr,
    double? price,
    List<ProductImage>? images,
    int? stockQuantity,
    ProductCategory? category,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    double? weightGrams,
    String? dimensions,
  }) {
    return Product(
      id: id ?? this.id,
      vendorId: vendorId ?? this.vendorId,
      nameAr: nameAr ?? this.nameAr,
      descriptionAr: descriptionAr ?? this.descriptionAr,
      price: price ?? this.price,
      images: images ?? this.images,
      stockQuantity: stockQuantity ?? this.stockQuantity,
      category: category ?? this.category,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      weightGrams: weightGrams ?? this.weightGrams,
      dimensions: dimensions ?? this.dimensions,
    );
  }

  @override
  List<Object?> get props => [
        id,
        vendorId,
        nameAr,
        descriptionAr,
        price,
        images,
        stockQuantity,
        category,
        isActive,
        createdAt,
        updatedAt,
        weightGrams,
        dimensions,
      ];
}
