// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProductImage _$ProductImageFromJson(Map<String, dynamic> json) => ProductImage(
      url: json['url'] as String,
      blurHash: json['blurHash'] as String,
      width: (json['width'] as num).toInt(),
      height: (json['height'] as num).toInt(),
      sortOrder: (json['sortOrder'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$ProductImageToJson(ProductImage instance) =>
    <String, dynamic>{
      'url': instance.url,
      'blurHash': instance.blurHash,
      'width': instance.width,
      'height': instance.height,
      'sortOrder': instance.sortOrder,
    };

Product _$ProductFromJson(Map<String, dynamic> json) => Product(
      id: json['id'] as String,
      vendorId: json['vendorId'] as String,
      nameAr: json['nameAr'] as String,
      descriptionAr: json['descriptionAr'] as String?,
      price: (json['price'] as num).toDouble(),
      images: (json['images'] as List<dynamic>?)
              ?.map((e) => ProductImage.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      stockQuantity: (json['stockQuantity'] as num).toInt(),
      category: $enumDecode(_$ProductCategoryEnumMap, json['category']),
      isActive: json['isActive'] as bool? ?? true,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      weightGrams: (json['weightGrams'] as num?)?.toDouble(),
      dimensions: json['dimensions'] as String?,
    );

Map<String, dynamic> _$ProductToJson(Product instance) => <String, dynamic>{
      'id': instance.id,
      'vendorId': instance.vendorId,
      'nameAr': instance.nameAr,
      'descriptionAr': instance.descriptionAr,
      'price': instance.price,
      'images': instance.images,
      'stockQuantity': instance.stockQuantity,
      'category': _$ProductCategoryEnumMap[instance.category]!,
      'isActive': instance.isActive,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'weightGrams': instance.weightGrams,
      'dimensions': instance.dimensions,
    };

const _$ProductCategoryEnumMap = {
  ProductCategory.food: 'food',
  ProductCategory.electronics: 'electronics',
  ProductCategory.clothing: 'clothing',
  ProductCategory.home: 'home',
  ProductCategory.beauty: 'beauty',
  ProductCategory.books: 'books',
  ProductCategory.sports: 'sports',
  ProductCategory.automotive: 'automotive',
  ProductCategory.toys: 'toys',
  ProductCategory.other: 'other',
};
