import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';
import 'user.dart';
import '../enums/enums.dart';

part 'courier.g.dart';

/// Model representing a location with latitude and longitude
@JsonSerializable()
class Location extends Equatable {
  /// Latitude coordinate
  final double latitude;

  /// Longitude coordinate
  final double longitude;

  /// Timestamp when the location was recorded
  final DateTime timestamp;

  const Location({
    required this.latitude,
    required this.longitude,
    required this.timestamp,
  });

  /// Creates a Location from JSON
  factory Location.fromJson(Map<String, dynamic> json) =>
      _$LocationFromJson(json);

  /// Converts Location to JSON
  Map<String, dynamic> toJson() => _$LocationToJson(this);

  @override
  List<Object?> get props => [latitude, longitude, timestamp];
}

/// Model representing a courier user
@JsonSerializable()
class Courier extends User {
  /// Courier's phone number
  final String phoneNumber;

  /// Type of vehicle used for deliveries
  final String vehicleType;

  /// Whether the courier is currently available for deliveries
  final bool isAvailable;

  /// Current location of the courier (optional)
  final Location? currentLocation;

  /// Total number of deliveries completed
  final int totalDeliveries;

  /// Average rating of the courier (0.0 to 5.0)
  final double rating;

  /// Total number of ratings received
  final int totalRatings;

  const Courier({
    required super.id,
    required super.email,
    required super.name,
    required super.createdAt,
    required super.updatedAt,
    required this.phoneNumber,
    required this.vehicleType,
    this.isAvailable = true,
    this.currentLocation,
    this.totalDeliveries = 0,
    this.rating = 0.0,
    this.totalRatings = 0,
  }) : super(type: UserType.courier);

  /// Creates a Courier from JSON
  factory Courier.fromJson(Map<String, dynamic> json) =>
      _$CourierFromJson(json);

  /// Converts Courier to JSON
  @override
  Map<String, dynamic> toJson() => _$CourierToJson(this);

  /// Returns the availability status in Arabic
  String get availabilityStatusAr => isAvailable ? 'متاح' : 'غير متاح';

  /// Returns true if the courier has a good rating (>= 4.0)
  bool get hasGoodRating => rating >= 4.0 && totalRatings > 0;

  /// Returns the rating as a formatted string
  String get formattedRating {
    if (totalRatings == 0) return 'لا توجد تقييمات';
    return '${rating.toStringAsFixed(1)} (${totalRatings} تقييم)';
  }

  /// Returns experience level based on total deliveries
  String get experienceLevelAr {
    if (totalDeliveries < 10) return 'مبتدئ';
    if (totalDeliveries < 50) return 'متوسط';
    if (totalDeliveries < 200) return 'متقدم';
    return 'خبير';
  }

  /// Creates a copy of this courier with updated fields
  @override
  Courier copyWith({
    String? id,
    String? email,
    String? name,
    UserType? type,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? phoneNumber,
    String? vehicleType,
    bool? isAvailable,
    Location? currentLocation,
    int? totalDeliveries,
    double? rating,
    int? totalRatings,
  }) {
    return Courier(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      vehicleType: vehicleType ?? this.vehicleType,
      isAvailable: isAvailable ?? this.isAvailable,
      currentLocation: currentLocation ?? this.currentLocation,
      totalDeliveries: totalDeliveries ?? this.totalDeliveries,
      rating: rating ?? this.rating,
      totalRatings: totalRatings ?? this.totalRatings,
    );
  }

  @override
  List<Object?> get props => [
        ...super.props,
        phoneNumber,
        vehicleType,
        isAvailable,
        currentLocation,
        totalDeliveries,
        rating,
        totalRatings,
      ];
}
