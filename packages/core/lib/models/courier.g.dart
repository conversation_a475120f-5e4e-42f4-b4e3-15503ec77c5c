// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'courier.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Location _$LocationFromJson(Map<String, dynamic> json) => Location(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      timestamp: DateTime.parse(json['timestamp'] as String),
    );

Map<String, dynamic> _$LocationToJson(Location instance) => <String, dynamic>{
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'timestamp': instance.timestamp.toIso8601String(),
    };

Courier _$CourierFromJson(Map<String, dynamic> json) => Courier(
      id: json['id'] as String,
      email: json['email'] as String,
      name: json['name'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      phoneNumber: json['phoneNumber'] as String,
      vehicleType: json['vehicleType'] as String,
      isAvailable: json['isAvailable'] as bool? ?? true,
      currentLocation: json['currentLocation'] == null
          ? null
          : Location.fromJson(json['currentLocation'] as Map<String, dynamic>),
      totalDeliveries: (json['totalDeliveries'] as num?)?.toInt() ?? 0,
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      totalRatings: (json['totalRatings'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$CourierToJson(Courier instance) => <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'name': instance.name,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'phoneNumber': instance.phoneNumber,
      'vehicleType': instance.vehicleType,
      'isAvailable': instance.isAvailable,
      'currentLocation': instance.currentLocation,
      'totalDeliveries': instance.totalDeliveries,
      'rating': instance.rating,
      'totalRatings': instance.totalRatings,
    };
