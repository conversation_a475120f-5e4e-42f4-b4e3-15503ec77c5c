// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vendor.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Vendor _$VendorFromJson(Map<String, dynamic> json) => Vendor(
      id: json['id'] as String,
      email: json['email'] as String,
      name: json['name'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      businessName: json['businessName'] as String,
      category: $enumDecode(_$VendorCategoryEnumMap, json['category']),
      description: json['description'] as String?,
      businessAddress:
          Address.fromJson(json['businessAddress'] as Map<String, dynamic>),
      isVerified: json['isVerified'] as bool? ?? false,
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      totalRatings: (json['totalRatings'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$VendorToJson(Vendor instance) => <String, dynamic>{
      'id': instance.id,
      'email': instance.email,
      'name': instance.name,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'businessName': instance.businessName,
      'category': _$VendorCategoryEnumMap[instance.category]!,
      'description': instance.description,
      'businessAddress': instance.businessAddress,
      'isVerified': instance.isVerified,
      'rating': instance.rating,
      'totalRatings': instance.totalRatings,
    };

const _$VendorCategoryEnumMap = {
  VendorCategory.foodBeverages: 'food_beverages',
  VendorCategory.electronics: 'electronics',
  VendorCategory.clothingFashion: 'clothing_fashion',
  VendorCategory.homeGarden: 'home_garden',
  VendorCategory.healthBeauty: 'health_beauty',
  VendorCategory.booksMedia: 'books_media',
  VendorCategory.sportsOutdoors: 'sports_outdoors',
  VendorCategory.automotive: 'automotive',
  VendorCategory.toysGames: 'toys_games',
  VendorCategory.services: 'services',
};
