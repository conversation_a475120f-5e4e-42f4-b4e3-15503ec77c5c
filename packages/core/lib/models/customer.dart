import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';
import 'user.dart';
import 'address.dart';
import '../enums/enums.dart';

part 'customer.g.dart';

/// Model representing a customer user
@JsonSerializable()
class Customer extends User {
  /// Customer's phone number (optional)
  final String? phoneNumber;

  /// List of customer's saved addresses
  @JsonKey(toJson: _addressesToJson, fromJson: _addressesFromJson)
  final List<Address> addresses;

  /// List of favorite vendor IDs
  final List<String> favoriteVendors;

  const Customer({
    required super.id,
    required super.email,
    required super.name,
    required super.createdAt,
    required super.updatedAt,
    this.phoneNumber,
    this.addresses = const [],
    this.favoriteVendors = const [],
  }) : super(type: UserType.customer);

  /// Creates a Customer from JSON
  factory Customer.fromJson(Map<String, dynamic> json) =>
      _$CustomerFromJson(json);

  /// Converts Customer to JSO<PERSON>
  @override
  Map<String, dynamic> toJson() => _$CustomerToJson(this);

  /// Gets the default address for the customer
  Address? get defaultAddress {
    try {
      return addresses.firstWhere((address) => address.isDefault);
    } catch (e) {
      return addresses.isNotEmpty ? addresses.first : null;
    }
  }

  /// Checks if a vendor is in favorites
  bool isFavoriteVendor(String vendorId) {
    return favoriteVendors.contains(vendorId);
  }

  /// Creates a copy of this customer with updated fields
  @override
  Customer copyWith({
    String? id,
    String? email,
    String? name,
    UserType? type,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? phoneNumber,
    List<Address>? addresses,
    List<String>? favoriteVendors,
  }) {
    return Customer(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      addresses: addresses ?? this.addresses,
      favoriteVendors: favoriteVendors ?? this.favoriteVendors,
    );
  }

  @override
  List<Object?> get props => [
        ...super.props,
        phoneNumber,
        addresses,
        favoriteVendors,
      ];
}

/// Helper function to serialize addresses to JSON
List<Map<String, dynamic>> _addressesToJson(List<Address> addresses) {
  return addresses.map((address) => address.toJson()).toList();
}

/// Helper function to deserialize addresses from JSON
List<Address> _addressesFromJson(List<dynamic>? json) {
  if (json == null) return [];
  return json
      .map((item) => Address.fromJson(item as Map<String, dynamic>))
      .toList();
}
