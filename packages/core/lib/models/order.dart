import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';
import 'address.dart';
import '../enums/enums.dart';

part 'order.g.dart';

/// Model representing an item within an order
@JsonSerializable()
class OrderItem extends Equatable {
  /// Unique identifier for the order item
  final String id;

  /// ID of the order this item belongs to
  final String orderId;

  /// ID of the product
  final String productId;

  /// Product name (cached for historical purposes)
  final String productName;

  /// Quantity ordered
  final int quantity;

  /// Unit price at the time of order
  final double unitPrice;

  /// Total price for this item (quantity * unitPrice)
  final double totalPrice;

  const OrderItem({
    required this.id,
    required this.orderId,
    required this.productId,
    required this.productName,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
  });

  /// Creates an OrderItem from JSON
  factory OrderItem.fromJson(Map<String, dynamic> json) =>
      _$OrderItemFromJson(json);

  /// Converts OrderItem to JSON
  Map<String, dynamic> toJson() => _$OrderItemToJson(this);

  /// Returns the formatted unit price as a string
  String get formattedUnitPrice => '${unitPrice.toStringAsFixed(2)} ر.س';

  /// Returns the formatted total price as a string
  String get formattedTotalPrice => '${totalPrice.toStringAsFixed(2)} ر.س';

  /// Creates a copy of this order item with updated fields
  OrderItem copyWith({
    String? id,
    String? orderId,
    String? productId,
    String? productName,
    int? quantity,
    double? unitPrice,
    double? totalPrice,
  }) {
    return OrderItem(
      id: id ?? this.id,
      orderId: orderId ?? this.orderId,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      totalPrice: totalPrice ?? this.totalPrice,
    );
  }

  @override
  List<Object?> get props => [
        id,
        orderId,
        productId,
        productName,
        quantity,
        unitPrice,
        totalPrice,
      ];
}

/// Model representing an order
@JsonSerializable()
class Order extends Equatable {
  /// Unique identifier for the order
  final String id;

  /// ID of the customer who placed the order
  final String customerId;

  /// ID of the vendor fulfilling the order
  final String vendorId;

  /// ID of the courier assigned to deliver the order (optional)
  final String? courierId;

  /// List of items in the order
  final List<OrderItem> items;

  /// Total amount for the order
  final double totalAmount;

  /// Current status of the order
  final OrderStatus status;

  /// Delivery address
  final Address deliveryAddress;

  /// Payment method (always cash on delivery)
  final PaymentMethod paymentMethod;

  /// When the order was created
  final DateTime createdAt;

  /// When the order was delivered (optional)
  final DateTime? deliveredAt;

  /// Special instructions for the order
  final String? specialInstructions;

  /// Estimated delivery time
  final DateTime? estimatedDeliveryTime;

  const Order({
    required this.id,
    required this.customerId,
    required this.vendorId,
    this.courierId,
    required this.items,
    required this.totalAmount,
    required this.status,
    required this.deliveryAddress,
    this.paymentMethod = PaymentMethod.cashOnDelivery,
    required this.createdAt,
    this.deliveredAt,
    this.specialInstructions,
    this.estimatedDeliveryTime,
  });

  /// Creates an Order from JSON
  factory Order.fromJson(Map<String, dynamic> json) => _$OrderFromJson(json);

  /// Converts Order to JSON
  Map<String, dynamic> toJson() => _$OrderToJson(this);

  /// Returns the total number of items in the order
  int get totalItems => items.fold(0, (sum, item) => sum + item.quantity);

  /// Returns the formatted total amount as a string
  String get formattedTotalAmount => '${totalAmount.toStringAsFixed(2)} ر.س';

  /// Returns true if the order can be cancelled
  bool get canBeCancelled => status.canBeCancelled;

  /// Returns true if the order is in a final state
  bool get isFinal => status.isFinal;

  /// Returns true if the order has been delivered
  bool get isDelivered => status == OrderStatus.delivered;

  /// Returns true if the order is cancelled
  bool get isCancelled => status == OrderStatus.cancelled;

  /// Returns the order duration (time since creation)
  Duration get orderDuration => DateTime.now().difference(createdAt);

  /// Returns the delivery duration (time from creation to delivery)
  Duration? get deliveryDuration {
    if (deliveredAt == null) return null;
    return deliveredAt!.difference(createdAt);
  }

  /// Creates a copy of this order with updated fields
  Order copyWith({
    String? id,
    String? customerId,
    String? vendorId,
    String? courierId,
    List<OrderItem>? items,
    double? totalAmount,
    OrderStatus? status,
    Address? deliveryAddress,
    PaymentMethod? paymentMethod,
    DateTime? createdAt,
    DateTime? deliveredAt,
    String? specialInstructions,
    DateTime? estimatedDeliveryTime,
  }) {
    return Order(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      vendorId: vendorId ?? this.vendorId,
      courierId: courierId ?? this.courierId,
      items: items ?? this.items,
      totalAmount: totalAmount ?? this.totalAmount,
      status: status ?? this.status,
      deliveryAddress: deliveryAddress ?? this.deliveryAddress,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      createdAt: createdAt ?? this.createdAt,
      deliveredAt: deliveredAt ?? this.deliveredAt,
      specialInstructions: specialInstructions ?? this.specialInstructions,
      estimatedDeliveryTime:
          estimatedDeliveryTime ?? this.estimatedDeliveryTime,
    );
  }

  @override
  List<Object?> get props => [
        id,
        customerId,
        vendorId,
        courierId,
        items,
        totalAmount,
        status,
        deliveryAddress,
        paymentMethod,
        createdAt,
        deliveredAt,
        specialInstructions,
        estimatedDeliveryTime,
      ];
}
