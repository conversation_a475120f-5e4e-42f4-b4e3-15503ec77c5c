import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';
import 'user.dart';
import 'address.dart';
import '../enums/enums.dart';

part 'vendor.g.dart';

/// Model representing a vendor user
@JsonSerializable()
class Vendor extends User {
  /// Business name of the vendor
  final String businessName;

  /// Category of the vendor's business
  final VendorCategory category;

  /// Description of the vendor's business
  final String? description;

  /// Business address
  final Address businessAddress;

  /// Whether the vendor is verified by the platform
  final bool isVerified;

  /// Average rating of the vendor (0.0 to 5.0)
  final double rating;

  /// Total number of ratings received
  final int totalRatings;

  const Vendor({
    required super.id,
    required super.email,
    required super.name,
    required super.createdAt,
    required super.updatedAt,
    required this.businessName,
    required this.category,
    this.description,
    required this.businessAddress,
    this.isVerified = false,
    this.rating = 0.0,
    this.totalRatings = 0,
  }) : super(type: UserType.vendor);

  /// Creates a Vendor from JSON
  factory Vendor.fromJson(Map<String, dynamic> json) => _$VendorFromJson(json);

  /// Converts Vendor to JSON
  @override
  Map<String, dynamic> toJson() => _$VendorToJson(this);

  /// Returns the display name for the vendor (business name or user name)
  String get displayName => businessName.isNotEmpty ? businessName : name;

  /// Returns true if the vendor has a good rating (>= 4.0)
  bool get hasGoodRating => rating >= 4.0 && totalRatings > 0;

  /// Returns the rating as a formatted string
  String get formattedRating {
    if (totalRatings == 0) return 'لا توجد تقييمات';
    return '${rating.toStringAsFixed(1)} (${totalRatings} تقييم)';
  }

  /// Creates a copy of this vendor with updated fields
  @override
  Vendor copyWith({
    String? id,
    String? email,
    String? name,
    UserType? type,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? businessName,
    VendorCategory? category,
    String? description,
    Address? businessAddress,
    bool? isVerified,
    double? rating,
    int? totalRatings,
  }) {
    return Vendor(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      businessName: businessName ?? this.businessName,
      category: category ?? this.category,
      description: description ?? this.description,
      businessAddress: businessAddress ?? this.businessAddress,
      isVerified: isVerified ?? this.isVerified,
      rating: rating ?? this.rating,
      totalRatings: totalRatings ?? this.totalRatings,
    );
  }

  @override
  List<Object?> get props => [
        ...super.props,
        businessName,
        category,
        description,
        businessAddress,
        isVerified,
        rating,
        totalRatings,
      ];
}
