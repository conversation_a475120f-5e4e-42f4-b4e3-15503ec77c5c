import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';
import '../enums/enums.dart';

part 'user.g.dart';

/// Base abstract class for all user types
@JsonSerializable()
class User extends Equatable {
  /// Unique identifier for the user
  final String id;

  /// User's email address
  final String email;

  /// User's full name
  final String name;

  /// Type of user (customer, vendor, courier)
  final UserType type;

  /// When the user account was created
  final DateTime createdAt;

  /// When the user account was last updated
  final DateTime updatedAt;

  const User({
    required this.id,
    required this.email,
    required this.name,
    required this.type,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Creates a User from JSON
  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  /// Converts User to JSON
  Map<String, dynamic> toJson() => _$UserToJson(this);

  /// Creates a copy of this user with updated fields
  User copyWith({
    String? id,
    String? email,
    String? name,
    UserType? type,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      type: type ?? this.type,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [id, email, name, type, createdAt, updatedAt];
}
