import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'address.g.dart';

/// Model representing a physical address
@JsonSerializable()
class Address extends Equatable {
  /// Unique identifier for the address
  final String? id;

  /// Street address line 1
  final String street1;

  /// Street address line 2 (optional)
  final String? street2;

  /// City name
  final String city;

  /// State or province
  final String state;

  /// Postal code
  final String postalCode;

  /// Country name
  final String country;

  /// Latitude coordinate
  final double? latitude;

  /// Longitude coordinate
  final double? longitude;

  /// Additional notes or instructions
  final String? notes;

  /// Whether this is the default address
  final bool isDefault;

  const Address({
    this.id,
    required this.street1,
    this.street2,
    required this.city,
    required this.state,
    required this.postalCode,
    required this.country,
    this.latitude,
    this.longitude,
    this.notes,
    this.isDefault = false,
  });

  /// Creates an Address from JSON
  factory Address.fromJson(Map<String, dynamic> json) =>
      _$AddressFromJson(json);

  /// Converts Address to JSON
  Map<String, dynamic> toJson() => _$AddressToJson(this);

  /// Returns the full address as a single string
  String get fullAddress {
    final parts = <String>[
      street1,
      if (street2?.isNotEmpty == true) street2!,
      city,
      state,
      postalCode,
      country,
    ];
    return parts.join(', ');
  }

  /// Returns a short version of the address
  String get shortAddress {
    return '$street1, $city';
  }

  /// Creates a copy of this address with updated fields
  Address copyWith({
    String? id,
    String? street1,
    String? street2,
    String? city,
    String? state,
    String? postalCode,
    String? country,
    double? latitude,
    double? longitude,
    String? notes,
    bool? isDefault,
  }) {
    return Address(
      id: id ?? this.id,
      street1: street1 ?? this.street1,
      street2: street2 ?? this.street2,
      city: city ?? this.city,
      state: state ?? this.state,
      postalCode: postalCode ?? this.postalCode,
      country: country ?? this.country,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      notes: notes ?? this.notes,
      isDefault: isDefault ?? this.isDefault,
    );
  }

  @override
  List<Object?> get props => [
        id,
        street1,
        street2,
        city,
        state,
        postalCode,
        country,
        latitude,
        longitude,
        notes,
        isDefault,
      ];
}
