import 'package:flutter_test/flutter_test.dart';
import 'package:core/core.dart';

void main() {
  group('Core Package Tests', () {
    test('should export all modules correctly', () {
      // Test that all exports are accessible
      expect(AppConstants.appName, isNotEmpty);
      expect(UserType.values, isNotEmpty);
      expect(OrderStatus.values, isNotEmpty);
      expect(VendorCategory.values, isNotEmpty);
      expect(PaymentMethod.values, isNotEmpty);
    });
  });
}
