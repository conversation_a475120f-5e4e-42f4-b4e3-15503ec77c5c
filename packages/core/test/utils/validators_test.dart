import 'package:flutter_test/flutter_test.dart';
import 'package:core/core.dart';

void main() {
  group('Validators', () {
    group('validateEmail', () {
      test('should return null for valid emails', () {
        expect(Validators.validateEmail('<EMAIL>'), isNull);
        expect(Validators.validateEmail('<EMAIL>'), isNull);
        expect(Validators.validateEmail('<EMAIL>'), isNull);
      });

      test('should return error for invalid emails', () {
        expect(Validators.validateEmail(''), isNotNull);
        expect(Validators.validateEmail(null), isNotNull);
        expect(Validators.validateEmail('invalid-email'), isNotNull);
        expect(Validators.validateEmail('test@'), isNotNull);
        expect(Validators.validateEmail('@example.com'), isNotNull);
        expect(Validators.validateEmail('test@.com'), isNotNull);
      });
    });

    group('validatePhone', () {
      test('should return null for valid phone numbers', () {
        expect(Validators.validatePhone('+966501234567'), isNull);
        expect(Validators.validatePhone('966501234567'), isNull);
        expect(Validators.validatePhone('0501234567'), isNull);
        expect(Validators.validatePhone('+1234567890'), isNull);
      });

      test('should return error for invalid phone numbers', () {
        expect(Validators.validatePhone(''), isNotNull);
        expect(Validators.validatePhone(null), isNotNull);
        expect(Validators.validatePhone('123'), isNotNull);
        expect(Validators.validatePhone('12345678901234567890'), isNotNull);
        expect(Validators.validatePhone('abc123'), isNotNull);
      });

      test('should handle phone numbers with spaces and formatting', () {
        expect(Validators.validatePhone('+966 50 123 4567'), isNull);
        expect(Validators.validatePhone('************'), isNull);
        expect(Validators.validatePhone('(*************'), isNull);
      });
    });

    group('validateRequired', () {
      test('should return null for non-empty values', () {
        expect(Validators.validateRequired('test'), isNull);
        expect(Validators.validateRequired('  test  '), isNull);
      });

      test('should return error for empty values', () {
        expect(Validators.validateRequired(''), isNotNull);
        expect(Validators.validateRequired(null), isNotNull);
        expect(Validators.validateRequired('   '), isNotNull);
      });

      test('should use custom field name in error message', () {
        final error = Validators.validateRequired('', fieldName: 'الاسم');
        expect(error, contains('الاسم'));
      });
    });

    group('validateName', () {
      test('should return null for valid names', () {
        expect(Validators.validateName('أحمد محمد'), isNull);
        expect(Validators.validateName('Ahmed Mohammed'), isNull);
        expect(Validators.validateName('John Doe'), isNull);
      });

      test('should return error for invalid names', () {
        expect(Validators.validateName(''), isNotNull);
        expect(Validators.validateName(null), isNotNull);
        expect(Validators.validateName('   '), isNotNull);
        expect(Validators.validateName('123456'), isNotNull);
      });

      test('should return error for names that are too long', () {
        final longName = 'a' * 101;
        expect(Validators.validateName(longName), isNotNull);
      });
    });

    group('validatePrice', () {
      test('should return null for valid prices', () {
        expect(Validators.validatePrice('10.50'), isNull);
        expect(Validators.validatePrice('100'), isNull);
        expect(Validators.validatePrice('0.01'), isNull);
      });

      test('should return error for invalid prices', () {
        expect(Validators.validatePrice(''), isNotNull);
        expect(Validators.validatePrice(null), isNotNull);
        expect(Validators.validatePrice('abc'), isNotNull);
        expect(Validators.validatePrice('0'), isNotNull);
        expect(Validators.validatePrice('-10'), isNotNull);
      });

      test('should return error for prices that are too high', () {
        expect(Validators.validatePrice('20000'), isNotNull);
      });
    });

    group('validateQuantity', () {
      test('should return null for valid quantities', () {
        expect(Validators.validateQuantity('1'), isNull);
        expect(Validators.validateQuantity('10'), isNull);
        expect(Validators.validateQuantity('999'), isNull);
      });

      test('should return error for invalid quantities', () {
        expect(Validators.validateQuantity(''), isNotNull);
        expect(Validators.validateQuantity(null), isNotNull);
        expect(Validators.validateQuantity('abc'), isNotNull);
        expect(Validators.validateQuantity('0'), isNotNull);
        expect(Validators.validateQuantity('-1'), isNotNull);
        expect(Validators.validateQuantity('1001'), isNotNull);
      });
    });

    group('validateAddress', () {
      test('should return null for valid addresses', () {
        expect(
            Validators.validateAddress('123 King Fahd Road, Riyadh'), isNull);
        expect(Validators.validateAddress('شارع الملك فهد، الرياض'), isNull);
      });

      test('should return error for invalid addresses', () {
        expect(Validators.validateAddress(''), isNotNull);
        expect(Validators.validateAddress(null), isNotNull);
        expect(Validators.validateAddress('   '), isNotNull);
        expect(Validators.validateAddress('short'), isNotNull);
      });

      test('should return error for addresses that are too long', () {
        final longAddress = 'a' * 201;
        expect(Validators.validateAddress(longAddress), isNotNull);
      });
    });

    group('validateRating', () {
      test('should return null for valid ratings', () {
        expect(Validators.validateRating(1.0), isNull);
        expect(Validators.validateRating(3.5), isNull);
        expect(Validators.validateRating(5.0), isNull);
      });

      test('should return error for invalid ratings', () {
        expect(Validators.validateRating(null), isNotNull);
        expect(Validators.validateRating(0.5), isNotNull);
        expect(Validators.validateRating(5.5), isNotNull);
        expect(Validators.validateRating(-1.0), isNotNull);
      });
    });

    group('validateOrderAmount', () {
      test('should return null for valid order amounts', () {
        expect(Validators.validateOrderAmount(10.0), isNull);
        expect(Validators.validateOrderAmount(100.0), isNull);
        expect(Validators.validateOrderAmount(5000.0), isNull);
      });

      test('should return error for invalid order amounts', () {
        expect(Validators.validateOrderAmount(null), isNotNull);
        expect(Validators.validateOrderAmount(5.0), isNotNull);
        expect(Validators.validateOrderAmount(15000.0), isNotNull);
      });
    });

    group('validateImageSize', () {
      test('should return null for valid image sizes', () {
        expect(Validators.validateImageSize(1024 * 1024), isNull); // 1MB
        expect(Validators.validateImageSize(3 * 1024 * 1024), isNull); // 3MB
      });

      test('should return error for images that are too large', () {
        expect(Validators.validateImageSize(6 * 1024 * 1024), isNotNull); // 6MB
      });
    });

    group('validateImageFormat', () {
      test('should return null for valid image formats', () {
        expect(Validators.validateImageFormat('image.jpg'), isNull);
        expect(Validators.validateImageFormat('photo.jpeg'), isNull);
        expect(Validators.validateImageFormat('picture.png'), isNull);
        expect(Validators.validateImageFormat('image.webp'), isNull);
      });

      test('should return error for invalid image formats', () {
        expect(Validators.validateImageFormat('document.pdf'), isNotNull);
        expect(Validators.validateImageFormat('video.mp4'), isNotNull);
        expect(Validators.validateImageFormat('file.txt'), isNotNull);
      });
    });

    group('validateArabicText', () {
      test('should return null for valid Arabic text', () {
        expect(Validators.validateArabicText('أحمد محمد'), isNull);
        expect(Validators.validateArabicText('مرحبا بكم'), isNull);
        expect(Validators.validateArabicText('النص العربي 123'), isNull);
      });

      test('should return error for non-Arabic text', () {
        expect(Validators.validateArabicText(''), isNotNull);
        expect(Validators.validateArabicText(null), isNotNull);
        expect(Validators.validateArabicText('English Text'), isNotNull);
        expect(Validators.validateArabicText('123456'), isNotNull);
      });
    });

    group('validateCoordinate', () {
      test('should return null for valid coordinates', () {
        expect(
            Validators.validateCoordinate(24.7136, isLatitude: true), isNull);
        expect(
            Validators.validateCoordinate(46.6753, isLatitude: false), isNull);
        expect(Validators.validateCoordinate(-90.0, isLatitude: true), isNull);
        expect(Validators.validateCoordinate(180.0, isLatitude: false), isNull);
      });

      test('should return error for invalid coordinates', () {
        expect(
            Validators.validateCoordinate(null, isLatitude: true), isNotNull);
        expect(
            Validators.validateCoordinate(91.0, isLatitude: true), isNotNull);
        expect(
            Validators.validateCoordinate(-91.0, isLatitude: true), isNotNull);
        expect(
            Validators.validateCoordinate(181.0, isLatitude: false), isNotNull);
        expect(Validators.validateCoordinate(-181.0, isLatitude: false),
            isNotNull);
      });
    });

    group('combineValidators', () {
      test('should return first error from validators', () {
        final validators = [
          (String? value) => Validators.validateRequired(value),
          (String? value) => Validators.validateEmail(value),
        ];

        expect(Validators.combineValidators('', validators), isNotNull);
        expect(Validators.combineValidators('invalid-email', validators),
            isNotNull);
        expect(Validators.combineValidators('<EMAIL>', validators),
            isNull);
      });

      test('should return null when all validators pass', () {
        final validators = [
          (String? value) => Validators.validateRequired(value),
          (String? value) => Validators.validateName(value),
        ];

        expect(Validators.combineValidators('أحمد محمد', validators), isNull);
      });
    });
  });
}
