import 'package:flutter_test/flutter_test.dart';
import 'package:core/core.dart';

void main() {
  group('Helpers', () {
    group('generateRandomString', () {
      test('should generate string of correct length', () {
        expect(Helpers.generateRandomString(10).length, 10);
        expect(Helpers.generateRandomString(5).length, 5);
        expect(Helpers.generateRandomString(0).length, 0);
      });

      test('should generate different strings', () {
        final string1 = Helpers.generateRandomString(10);
        final string2 = Helpers.generateRandomString(10);
        expect(string1, isNot(equals(string2)));
      });
    });

    group('generateId', () {
      test('should generate ID of correct length', () {
        expect(Helpers.generateId().length, 16);
      });

      test('should generate unique IDs', () {
        final id1 = Helpers.generateId();
        final id2 = Helpers.generateId();
        expect(id1, isNot(equals(id2)));
      });
    });

    group('calculateDistance', () {
      test('should calculate distance between coordinates', () {
        // Distance between Riyadh and Jeddah (approximately 870 km)
        final distance = Helpers.calculateDistance(
          24.7136, 46.6753, // Riyadh
          21.4858, 39.1925, // Jeddah
        );
        expect(distance, greaterThan(800));
        expect(distance, lessThan(900));
      });

      test('should return zero for same coordinates', () {
        final distance = Helpers.calculateDistance(
          24.7136,
          46.6753,
          24.7136,
          46.6753,
        );
        expect(distance, closeTo(0, 0.1));
      });
    });

    group('calculateDeliveryFee', () {
      test('should return base fee for short distances', () {
        expect(Helpers.calculateDeliveryFee(3), AppConstants.deliveryFee);
        expect(Helpers.calculateDeliveryFee(5), AppConstants.deliveryFee);
      });

      test('should increase fee for longer distances', () {
        expect(Helpers.calculateDeliveryFee(8), AppConstants.deliveryFee * 1.5);
        expect(Helpers.calculateDeliveryFee(15), AppConstants.deliveryFee * 2);
        expect(Helpers.calculateDeliveryFee(25), AppConstants.deliveryFee * 3);
      });
    });

    group('qualifiesForFreeDelivery', () {
      test('should return true for orders above threshold', () {
        expect(Helpers.qualifiesForFreeDelivery(150), isTrue);
        expect(Helpers.qualifiesForFreeDelivery(100), isTrue);
      });

      test('should return false for orders below threshold', () {
        expect(Helpers.qualifiesForFreeDelivery(50), isFalse);
        expect(Helpers.qualifiesForFreeDelivery(99.99), isFalse);
      });
    });

    group('calculateEstimatedDeliveryTime', () {
      test('should return future time', () {
        final estimatedTime = Helpers.calculateEstimatedDeliveryTime(10);
        expect(estimatedTime.isAfter(DateTime.now()), isTrue);
      });

      test('should increase time for longer distances', () {
        final shortDistance = Helpers.calculateEstimatedDeliveryTime(5);
        final longDistance = Helpers.calculateEstimatedDeliveryTime(20);
        expect(longDistance.isAfter(shortDistance), isTrue);
      });
    });

    group('formatFileSize', () {
      test('should format bytes correctly', () {
        expect(Helpers.formatFileSize(512), '512 B');
        expect(Helpers.formatFileSize(1024), '1.0 KB');
        expect(Helpers.formatFileSize(1536), '1.5 KB');
        expect(Helpers.formatFileSize(1024 * 1024), '1.0 MB');
        expect(Helpers.formatFileSize(1024 * 1024 * 1024), '1.0 GB');
      });
    });

    group('generateColorFromString', () {
      test('should generate consistent colors for same string', () {
        final color1 = Helpers.generateColorFromString('test');
        final color2 = Helpers.generateColorFromString('test');
        expect(color1, equals(color2));
      });

      test('should generate different colors for different strings', () {
        final color1 = Helpers.generateColorFromString('test1');
        final color2 = Helpers.generateColorFromString('test2');
        expect(color1, isNot(equals(color2)));
      });

      test('should generate valid color values', () {
        final color = Helpers.generateColorFromString('test');
        expect(color, greaterThanOrEqualTo(0xFF000000));
        expect(color, lessThanOrEqualTo(0xFFFFFFFF));
      });
    });

    group('clamp', () {
      test('should clamp values correctly', () {
        expect(Helpers.clamp(5, 0, 10), 5);
        expect(Helpers.clamp(-5, 0, 10), 0);
        expect(Helpers.clamp(15, 0, 10), 10);
      });
    });

    group('isInRange', () {
      test('should check range correctly', () {
        expect(Helpers.isInRange(5, 0, 10), isTrue);
        expect(Helpers.isInRange(0, 0, 10), isTrue);
        expect(Helpers.isInRange(10, 0, 10), isTrue);
        expect(Helpers.isInRange(-1, 0, 10), isFalse);
        expect(Helpers.isInRange(11, 0, 10), isFalse);
      });
    });

    group('roundToDecimalPlaces', () {
      test('should round correctly', () {
        expect(Helpers.roundToDecimalPlaces(123.456, 2), 123.46);
        expect(Helpers.roundToDecimalPlaces(123.454, 2), 123.45);
        expect(Helpers.roundToDecimalPlaces(123.0, 2), 123.0);
      });
    });

    group('groupBy', () {
      test('should group items correctly', () {
        final items = ['apple', 'banana', 'apricot', 'blueberry'];
        final grouped = Helpers.groupBy(items, (item) => item[0]);

        expect(grouped['a'], ['apple', 'apricot']);
        expect(grouped['b'], ['banana', 'blueberry']);
      });
    });

    group('safeJsonParse', () {
      test('should parse valid JSON', () {
        final json = {'name': 'test', 'value': 123};
        final result =
            Helpers.safeJsonParse(json, (json) => json['name'] as String);
        expect(result, 'test');
      });

      test('should return null for invalid JSON', () {
        final result =
            Helpers.safeJsonParse('invalid', (json) => json['name'] as String);
        expect(result, isNull);
      });

      test('should return null when parsing throws', () {
        final json = {'name': 123}; // Wrong type
        final result = Helpers.safeJsonParse(
            json, (json) => (json['name'] as String).toUpperCase());
        expect(result, isNull);
      });
    });

    group('safeJsonParseList', () {
      test('should parse valid JSON list', () {
        final jsonList = [
          {'name': 'item1'},
          {'name': 'item2'},
        ];
        final result = Helpers.safeJsonParseList(
            jsonList, (json) => json['name'] as String);
        expect(result, ['item1', 'item2']);
      });

      test('should return empty list for invalid JSON', () {
        final result = Helpers.safeJsonParseList(
            'invalid', (json) => json['name'] as String);
        expect(result, isEmpty);
      });

      test('should filter out invalid items', () {
        final jsonList = [
          {'name': 'item1'},
          'invalid',
          {'name': 'item2'},
        ];
        final result = Helpers.safeJsonParseList(
            jsonList, (json) => json['name'] as String);
        expect(result, ['item1', 'item2']);
      });
    });

    group('delay', () {
      test('should create delay', () async {
        final stopwatch = Stopwatch()..start();
        await Helpers.delay(const Duration(milliseconds: 100));
        stopwatch.stop();
        expect(stopwatch.elapsedMilliseconds, greaterThanOrEqualTo(90));
      });
    });

    group('isWithinBusinessHours', () {
      test('should work with default hours', () {
        // This test depends on current time, so we just check it doesn't throw
        expect(() => Helpers.isWithinBusinessHours(), returnsNormally);
      });

      test('should work with custom hours', () {
        expect(() => Helpers.isWithinBusinessHours(startHour: 9, endHour: 17),
            returnsNormally);
      });
    });

    group('calculateBusinessDays', () {
      test('should calculate business days correctly', () {
        final monday = DateTime(2024, 3, 4); // Monday
        final thursday = DateTime(2024, 3, 7); // Thursday
        final businessDays = Helpers.calculateBusinessDays(monday, thursday);
        expect(businessDays, 4); // Monday to Thursday inclusive
      });

      test('should exclude weekends', () {
        final monday = DateTime(2024, 3, 4); // Monday
        final nextMonday = DateTime(2024, 3, 11); // Next Monday
        final businessDays = Helpers.calculateBusinessDays(monday, nextMonday);
        expect(businessDays, 6); // Excludes Friday and Saturday
      });

      test('should return 0 for end before start', () {
        final later = DateTime(2024, 3, 8);
        final earlier = DateTime(2024, 3, 4);
        final businessDays = Helpers.calculateBusinessDays(later, earlier);
        expect(businessDays, 0);
      });
    });
  });
}
