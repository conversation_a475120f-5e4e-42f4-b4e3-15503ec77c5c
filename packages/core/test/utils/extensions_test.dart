import 'package:flutter_test/flutter_test.dart';
import 'package:core/core.dart';

void main() {
  group('StringExtensions', () {
    test('isValidEmail should work correctly', () {
      expect('<EMAIL>'.isValidEmail, isTrue);
      expect('invalid-email'.isValidEmail, isFalse);
      expect(''.isValidEmail, isFalse);
    });

    test('isValidPhone should work correctly', () {
      expect('+966501234567'.isValidPhone, isTrue);
      expect('0501234567'.isValidPhone, isTrue);
      expect('123'.isValidPhone, isFalse);
      expect('abc123'.isValidPhone, isFalse);
    });

    test('hasArabicCharacters should work correctly', () {
      expect('أحمد محمد'.hasArabicCharacters, isTrue);
      expect('Ahmed Mohammed'.hasArabicCharacters, isFalse);
      expect('أحمد Ahmed'.hasArabicCharacters, isTrue);
      expect('123'.hasArabicCharacters, isFalse);
    });

    test('titleCase should work correctly', () {
      expect('hello world'.titleCase, 'Hello World');
      expect('HELLO WORLD'.titleCase, 'Hello World');
      expect('hELLo WoRLd'.titleCase, 'Hello World');
    });

    test('truncate should work correctly', () {
      expect('Hello World'.truncate(5), 'He...');
      expect('Hello'.truncate(10), 'Hello');
      expect('Hello World'.truncate(11), 'Hello World');
      expect('Hello World'.truncate(8, ellipsis: '***'), 'Hello***');
    });

    test('normalized should work correctly', () {
      expect('  hello   world  '.normalized, 'hello world');
      expect('hello\n\nworld'.normalized, 'hello world');
      expect('hello\t\tworld'.normalized, 'hello world');
    });

    test('toDoubleOrNull should work correctly', () {
      expect('123.45'.toDoubleOrNull, 123.45);
      expect('abc'.toDoubleOrNull, isNull);
      expect(''.toDoubleOrNull, isNull);
    });

    test('toIntOrNull should work correctly', () {
      expect('123'.toIntOrNull, 123);
      expect('123.45'.toIntOrNull, isNull);
      expect('abc'.toIntOrNull, isNull);
    });

    test('isNullOrEmpty should work correctly', () {
      expect(''.isNullOrEmpty, isTrue);
      expect('hello'.isNullOrEmpty, isFalse);
    });

    test('isNotNullOrEmpty should work correctly', () {
      expect(''.isNotNullOrEmpty, isFalse);
      expect('hello'.isNotNullOrEmpty, isTrue);
    });

    test('formattedPhone should work correctly', () {
      expect('+966501234567'.formattedPhone, '+966 50 123 4567');
      expect('+1234567890'.formattedPhone, '+1234567890');
      expect('invalid'.formattedPhone, 'invalid');
    });
  });

  group('DateTimeExtensions', () {
    late DateTime testDate;

    setUp(() {
      testDate = DateTime(2024, 3, 15, 14, 30, 0);
    });

    test('formattedDate should work correctly', () {
      expect(testDate.formattedDate, isNotEmpty);
    });

    test('formattedTime should work correctly', () {
      expect(testDate.formattedTime, isNotEmpty);
    });

    test('formattedDateTime should work correctly', () {
      expect(testDate.formattedDateTime, isNotEmpty);
    });

    test('relativeTime should work correctly', () {
      final now = DateTime.now();
      final oneHourAgo = now.subtract(const Duration(hours: 1));
      final oneDayAgo = now.subtract(const Duration(days: 1));
      final oneMinuteAgo = now.subtract(const Duration(minutes: 1));

      expect(oneHourAgo.relativeTime, contains('ساعة'));
      expect(oneDayAgo.relativeTime, contains('يوم'));
      expect(oneMinuteAgo.relativeTime, contains('دقيقة'));
    });

    test('isToday should work correctly', () {
      final today = DateTime.now();
      final yesterday = DateTime.now().subtract(const Duration(days: 1));

      expect(today.isToday, isTrue);
      expect(yesterday.isToday, isFalse);
    });

    test('isYesterday should work correctly', () {
      final yesterday = DateTime.now().subtract(const Duration(days: 1));
      final today = DateTime.now();

      expect(yesterday.isYesterday, isTrue);
      expect(today.isYesterday, isFalse);
    });

    test('isTomorrow should work correctly', () {
      final tomorrow = DateTime.now().add(const Duration(days: 1));
      final today = DateTime.now();

      expect(tomorrow.isTomorrow, isTrue);
      expect(today.isTomorrow, isFalse);
    });

    test('startOfDay should work correctly', () {
      final startOfDay = testDate.startOfDay;
      expect(startOfDay.hour, 0);
      expect(startOfDay.minute, 0);
      expect(startOfDay.second, 0);
      expect(startOfDay.day, testDate.day);
    });

    test('endOfDay should work correctly', () {
      final endOfDay = testDate.endOfDay;
      expect(endOfDay.hour, 23);
      expect(endOfDay.minute, 59);
      expect(endOfDay.second, 59);
      expect(endOfDay.day, testDate.day);
    });
  });

  group('DoubleExtensions', () {
    test('formattedPrice should work correctly', () {
      expect(123.45.formattedPrice, '123.45 ر.س');
      expect(100.0.formattedPrice, '100.00 ر.س');
    });

    test('formattedRating should work correctly', () {
      expect(4.5.formattedRating, '4.5');
      expect(3.0.formattedRating, '3.0');
    });

    test('formattedPercentage should work correctly', () {
      expect(0.75.formattedPercentage, '0.8%');
      expect(0.5.formattedPercentage, '0.5%');
    });

    test('formattedWeight should work correctly', () {
      expect(500.0.formattedWeight, '500 جرام');
      expect(1500.0.formattedWeight, '1.5 كيلو');
      expect(2000.0.formattedWeight, '2.0 كيلو');
    });

    test('roundToDecimalPlaces should work correctly', () {
      expect(123.456.roundToDecimalPlaces(2), 123.46);
      expect(123.454.roundToDecimalPlaces(2), 123.45);
      expect(123.0.roundToDecimalPlaces(2), 123.0);
    });
  });

  group('IntExtensions', () {
    test('formattedQuantity should work correctly', () {
      expect(1.formattedQuantity, 'قطعة واحدة');
      expect(2.formattedQuantity, 'قطعتان');
      expect(5.formattedQuantity, '5 قطع');
      expect(15.formattedQuantity, '15 قطعة');
    });

    test('formattedCount should work correctly', () {
      expect(0.formattedCount, 'لا يوجد');
      expect(1.formattedCount, 'واحد');
      expect(2.formattedCount, 'اثنان');
      expect(5.formattedCount, '5');
    });

    test('formattedBytes should work correctly', () {
      expect(512.formattedBytes, '512 بايت');
      expect(1536.formattedBytes, '1.5 كيلوبايت');
      expect((2 * 1024 * 1024).formattedBytes, '2.0 ميجابايت');
      expect((3 * 1024 * 1024 * 1024).formattedBytes, '3.0 جيجابايت');
    });
  });

  group('ListExtensions', () {
    test('safeElementAt should work correctly', () {
      final list = [1, 2, 3];
      expect(list.safeElementAt(1), 2);
      expect(list.safeElementAt(5), isNull);
      expect(list.safeElementAt(-1), isNull);
    });

    test('chunk should work correctly', () {
      final list = [1, 2, 3, 4, 5, 6, 7];
      final chunks = list.chunk(3);
      expect(chunks, [
        [1, 2, 3],
        [4, 5, 6],
        [7]
      ]);
    });

    test('unique should work correctly', () {
      final list = [1, 2, 2, 3, 3, 3, 4];
      expect(list.unique, [1, 2, 3, 4]);
    });
  });

  group('DurationExtensions', () {
    test('formattedDuration should work correctly', () {
      expect(const Duration(days: 1).formattedDuration, 'يوم واحد');
      expect(const Duration(days: 2).formattedDuration, '2 أيام');
      expect(const Duration(hours: 1).formattedDuration, 'ساعة واحدة');
      expect(const Duration(hours: 3).formattedDuration, '3 ساعات');
      expect(const Duration(minutes: 1).formattedDuration, 'دقيقة واحدة');
      expect(const Duration(minutes: 5).formattedDuration, '5 دقائق');
      expect(const Duration(seconds: 30).formattedDuration, 'أقل من دقيقة');
    });
  });
}
