import 'package:flutter_test/flutter_test.dart';
import 'package:core/core.dart';

void main() {
  group('Customer', () {
    late Customer testCustomer;
    late DateTime testCreatedAt;
    late DateTime testUpdatedAt;
    late List<Address> testAddresses;

    setUp(() {
      testCreatedAt = DateTime(2024, 1, 1, 10, 0, 0);
      testUpdatedAt = DateTime(2024, 1, 2, 15, 30, 0);

      testAddresses = [
        const Address(
          id: 'addr_1',
          street1: '123 King Fahd Road',
          city: 'Riyadh',
          state: 'Riyadh Province',
          postalCode: '12345',
          country: 'Saudi Arabia',
          isDefault: true,
        ),
        const Address(
          id: 'addr_2',
          street1: '456 Prince Sultan Road',
          city: 'Jeddah',
          state: 'Makkah Province',
          postalCode: '67890',
          country: 'Saudi Arabia',
          isDefault: false,
        ),
      ];

      testCustomer = Customer(
        id: 'customer_123',
        email: '<EMAIL>',
        name: 'أحمد محمد',
        createdAt: testCreatedAt,
        updatedAt: testUpdatedAt,
        phoneNumber: '+966501234567',
        addresses: testAddresses,
        favoriteVendors: ['vendor_1', 'vendor_2'],
      );
    });

    test('should create Customer with all properties', () {
      expect(testCustomer.id, 'customer_123');
      expect(testCustomer.email, '<EMAIL>');
      expect(testCustomer.name, 'أحمد محمد');
      expect(testCustomer.type, UserType.customer);
      expect(testCustomer.createdAt, testCreatedAt);
      expect(testCustomer.updatedAt, testUpdatedAt);
      expect(testCustomer.phoneNumber, '+966501234567');
      expect(testCustomer.addresses, testAddresses);
      expect(testCustomer.favoriteVendors, ['vendor_1', 'vendor_2']);
    });

    test('should create Customer with minimal properties', () {
      final minimalCustomer = Customer(
        id: 'customer_456',
        email: '<EMAIL>',
        name: 'فاطمة علي',
        createdAt: testCreatedAt,
        updatedAt: testUpdatedAt,
      );

      expect(minimalCustomer.phoneNumber, isNull);
      expect(minimalCustomer.addresses, isEmpty);
      expect(minimalCustomer.favoriteVendors, isEmpty);
      expect(minimalCustomer.type, UserType.customer);
    });

    test('should return correct default address', () {
      final defaultAddress = testCustomer.defaultAddress;
      expect(defaultAddress, isNotNull);
      expect(defaultAddress!.id, 'addr_1');
      expect(defaultAddress.isDefault, true);
    });

    test('should return first address when no default is set', () {
      final customerWithoutDefault = Customer(
        id: 'customer_789',
        email: '<EMAIL>',
        name: 'محمد أحمد',
        createdAt: testCreatedAt,
        updatedAt: testUpdatedAt,
        addresses: [
          const Address(
            id: 'addr_3',
            street1: '789 Al Olaya Street',
            city: 'Riyadh',
            state: 'Riyadh Province',
            postalCode: '11111',
            country: 'Saudi Arabia',
            isDefault: false,
          ),
        ],
      );

      final defaultAddress = customerWithoutDefault.defaultAddress;
      expect(defaultAddress, isNotNull);
      expect(defaultAddress!.id, 'addr_3');
    });

    test('should return null when no addresses exist', () {
      final customerWithoutAddresses = Customer(
        id: 'customer_000',
        email: '<EMAIL>',
        name: 'سارة أحمد',
        createdAt: testCreatedAt,
        updatedAt: testUpdatedAt,
      );

      expect(customerWithoutAddresses.defaultAddress, isNull);
    });

    test('should correctly identify favorite vendors', () {
      expect(testCustomer.isFavoriteVendor('vendor_1'), true);
      expect(testCustomer.isFavoriteVendor('vendor_2'), true);
      expect(testCustomer.isFavoriteVendor('vendor_3'), false);
      expect(testCustomer.isFavoriteVendor(''), false);
    });

    test('should serialize to JSON correctly', () {
      final json = testCustomer.toJson();

      expect(json['id'], 'customer_123');
      expect(json['email'], '<EMAIL>');
      expect(json['name'], 'أحمد محمد');
      // Note: type field is not serialized in subclasses, only in base User class
      expect(json['phoneNumber'], '+966501234567');
      expect(json['addresses'], isA<List>());
      expect(json['favoriteVendors'], ['vendor_1', 'vendor_2']);
    });

    test('should deserialize from JSON correctly', () {
      final json = {
        'id': 'customer_456',
        'email': '<EMAIL>',
        'name': 'فاطمة علي',
        'createdAt': '2024-02-01T12:00:00.000Z',
        'updatedAt': '2024-02-02T14:30:00.000Z',
        'phoneNumber': '+966509876543',
        'addresses': [
          {
            'id': 'addr_test',
            'street1': 'Test Street',
            'city': 'Test City',
            'state': 'Test State',
            'postalCode': '00000',
            'country': 'Saudi Arabia',
            'isDefault': true,
          }
        ],
        'favoriteVendors': ['vendor_test'],
      };

      final customer = Customer.fromJson(json);

      expect(customer.id, 'customer_456');
      expect(customer.email, '<EMAIL>');
      expect(customer.name, 'فاطمة علي');
      expect(customer.type, UserType.customer);
      expect(customer.phoneNumber, '+966509876543');
      expect(customer.addresses, hasLength(1));
      expect(customer.favoriteVendors, ['vendor_test']);
    });

    test('should create copy with updated fields', () {
      final updatedCustomer = testCustomer.copyWith(
        phoneNumber: '+966501111111',
        favoriteVendors: ['vendor_3', 'vendor_4'],
      );

      expect(updatedCustomer.id, testCustomer.id);
      expect(updatedCustomer.email, testCustomer.email);
      expect(updatedCustomer.name, testCustomer.name);
      expect(updatedCustomer.phoneNumber, '+966501111111');
      expect(updatedCustomer.favoriteVendors, ['vendor_3', 'vendor_4']);
      expect(updatedCustomer.addresses, testCustomer.addresses);
    });

    test('should support equality comparison', () {
      final customer1 = Customer(
        id: 'customer_123',
        email: '<EMAIL>',
        name: 'أحمد محمد',
        createdAt: testCreatedAt,
        updatedAt: testUpdatedAt,
        phoneNumber: '+966501234567',
        addresses: testAddresses,
        favoriteVendors: ['vendor_1', 'vendor_2'],
      );

      final customer2 = Customer(
        id: 'customer_123',
        email: '<EMAIL>',
        name: 'أحمد محمد',
        createdAt: testCreatedAt,
        updatedAt: testUpdatedAt,
        phoneNumber: '+966501234567',
        addresses: testAddresses,
        favoriteVendors: ['vendor_1', 'vendor_2'],
      );

      expect(customer1, equals(customer2));
    });

    test('should handle JSON serialization round trip', () {
      final json = testCustomer.toJson();
      final deserializedCustomer = Customer.fromJson(json);

      expect(deserializedCustomer, equals(testCustomer));
    });
  });
}
