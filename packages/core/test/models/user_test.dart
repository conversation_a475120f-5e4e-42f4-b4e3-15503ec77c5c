import 'package:flutter_test/flutter_test.dart';
import 'package:core/core.dart';

void main() {
  group('User', () {
    late User testUser;
    late DateTime testCreatedAt;
    late DateTime testUpdatedAt;

    setUp(() {
      testCreatedAt = DateTime(2024, 1, 1, 10, 0, 0);
      testUpdatedAt = DateTime(2024, 1, 2, 15, 30, 0);

      testUser = User(
        id: 'user_123',
        email: '<EMAIL>',
        name: 'أحمد محمد',
        type: UserType.customer,
        createdAt: testCreatedAt,
        updatedAt: testUpdatedAt,
      );
    });

    test('should create User with all properties', () {
      expect(testUser.id, 'user_123');
      expect(testUser.email, '<EMAIL>');
      expect(testUser.name, 'أحمد محمد');
      expect(testUser.type, UserType.customer);
      expect(testUser.createdAt, testCreatedAt);
      expect(testUser.updatedAt, testUpdatedAt);
    });

    test('should serialize to JSON correctly', () {
      final json = testUser.toJson();

      expect(json['id'], 'user_123');
      expect(json['email'], '<EMAIL>');
      expect(json['name'], 'أحمد محمد');
      expect(json['type'], 'customer');
      expect(json['createdAt'], testCreatedAt.toIso8601String());
      expect(json['updatedAt'], testUpdatedAt.toIso8601String());
    });

    test('should deserialize from JSON correctly', () {
      final json = {
        'id': 'user_456',
        'email': '<EMAIL>',
        'name': 'فاطمة علي',
        'type': 'vendor',
        'createdAt': '2024-02-01T12:00:00.000Z',
        'updatedAt': '2024-02-02T14:30:00.000Z',
      };

      final user = User.fromJson(json);

      expect(user.id, 'user_456');
      expect(user.email, '<EMAIL>');
      expect(user.name, 'فاطمة علي');
      expect(user.type, UserType.vendor);
      expect(user.createdAt, DateTime.parse('2024-02-01T12:00:00.000Z'));
      expect(user.updatedAt, DateTime.parse('2024-02-02T14:30:00.000Z'));
    });

    test('should create copy with updated fields', () {
      final updatedUser = testUser.copyWith(
        name: 'محمد أحمد',
        type: UserType.vendor,
      );

      expect(updatedUser.id, testUser.id);
      expect(updatedUser.email, testUser.email);
      expect(updatedUser.name, 'محمد أحمد');
      expect(updatedUser.type, UserType.vendor);
      expect(updatedUser.createdAt, testUser.createdAt);
      expect(updatedUser.updatedAt, testUser.updatedAt);
    });

    test('should support equality comparison', () {
      final user1 = User(
        id: 'user_123',
        email: '<EMAIL>',
        name: 'أحمد محمد',
        type: UserType.customer,
        createdAt: testCreatedAt,
        updatedAt: testUpdatedAt,
      );

      final user2 = User(
        id: 'user_123',
        email: '<EMAIL>',
        name: 'أحمد محمد',
        type: UserType.customer,
        createdAt: testCreatedAt,
        updatedAt: testUpdatedAt,
      );

      final user3 = User(
        id: 'user_456',
        email: '<EMAIL>',
        name: 'أحمد محمد',
        type: UserType.customer,
        createdAt: testCreatedAt,
        updatedAt: testUpdatedAt,
      );

      expect(user1, equals(user2));
      expect(user1, isNot(equals(user3)));
    });

    test('should handle JSON serialization round trip', () {
      final json = testUser.toJson();
      final deserializedUser = User.fromJson(json);

      expect(deserializedUser, equals(testUser));
    });
  });
}
