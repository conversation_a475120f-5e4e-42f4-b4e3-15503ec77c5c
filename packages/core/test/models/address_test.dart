import 'package:flutter_test/flutter_test.dart';
import 'package:core/core.dart';

void main() {
  group('Address', () {
    late Address testAddress;

    setUp(() {
      testAddress = const Address(
        id: 'addr_123',
        street1: '123 King Fahd Road',
        street2: 'Apartment 4B',
        city: 'Riyadh',
        state: 'Riyadh Province',
        postalCode: '12345',
        country: 'Saudi Arabia',
        latitude: 24.7136,
        longitude: 46.6753,
        notes: 'Near the mall',
        isDefault: true,
      );
    });

    test('should create Address with all properties', () {
      expect(testAddress.id, 'addr_123');
      expect(testAddress.street1, '123 King Fahd Road');
      expect(testAddress.street2, 'Apartment 4B');
      expect(testAddress.city, 'Riyadh');
      expect(testAddress.state, 'Riyadh Province');
      expect(testAddress.postalCode, '12345');
      expect(testAddress.country, 'Saudi Arabia');
      expect(testAddress.latitude, 24.7136);
      expect(testAddress.longitude, 46.6753);
      expect(testAddress.notes, 'Near the mall');
      expect(testAddress.isDefault, true);
    });

    test('should create Address with minimal required properties', () {
      const minimalAddress = Address(
        street1: '456 Main St',
        city: 'Jeddah',
        state: 'Makkah Province',
        postalCode: '54321',
        country: 'Saudi Arabia',
      );

      expect(minimalAddress.id, isNull);
      expect(minimalAddress.street2, isNull);
      expect(minimalAddress.latitude, isNull);
      expect(minimalAddress.longitude, isNull);
      expect(minimalAddress.notes, isNull);
      expect(minimalAddress.isDefault, false);
    });

    test('should return correct full address', () {
      const expected =
          '123 King Fahd Road, Apartment 4B, Riyadh, Riyadh Province, 12345, Saudi Arabia';
      expect(testAddress.fullAddress, expected);
    });

    test('should return correct short address', () {
      expect(testAddress.shortAddress, '123 King Fahd Road, Riyadh');
    });

    test('should handle full address without street2', () {
      const addressWithoutStreet2 = Address(
        street1: '123 King Fahd Road',
        city: 'Riyadh',
        state: 'Riyadh Province',
        postalCode: '12345',
        country: 'Saudi Arabia',
      );

      const expected =
          '123 King Fahd Road, Riyadh, Riyadh Province, 12345, Saudi Arabia';
      expect(addressWithoutStreet2.fullAddress, expected);
    });

    test('should serialize to JSON correctly', () {
      final json = testAddress.toJson();

      expect(json['id'], 'addr_123');
      expect(json['street1'], '123 King Fahd Road');
      expect(json['street2'], 'Apartment 4B');
      expect(json['city'], 'Riyadh');
      expect(json['state'], 'Riyadh Province');
      expect(json['postalCode'], '12345');
      expect(json['country'], 'Saudi Arabia');
      expect(json['latitude'], 24.7136);
      expect(json['longitude'], 46.6753);
      expect(json['notes'], 'Near the mall');
      expect(json['isDefault'], true);
    });

    test('should deserialize from JSON correctly', () {
      final json = {
        'id': 'addr_456',
        'street1': '789 Prince Sultan Road',
        'street2': null,
        'city': 'Jeddah',
        'state': 'Makkah Province',
        'postalCode': '67890',
        'country': 'Saudi Arabia',
        'latitude': 21.4858,
        'longitude': 39.1925,
        'notes': null,
        'isDefault': false,
      };

      final address = Address.fromJson(json);

      expect(address.id, 'addr_456');
      expect(address.street1, '789 Prince Sultan Road');
      expect(address.street2, isNull);
      expect(address.city, 'Jeddah');
      expect(address.state, 'Makkah Province');
      expect(address.postalCode, '67890');
      expect(address.country, 'Saudi Arabia');
      expect(address.latitude, 21.4858);
      expect(address.longitude, 39.1925);
      expect(address.notes, isNull);
      expect(address.isDefault, false);
    });

    test('should create copy with updated fields', () {
      final updatedAddress = testAddress.copyWith(
        city: 'Dammam',
        isDefault: false,
      );

      expect(updatedAddress.id, testAddress.id);
      expect(updatedAddress.street1, testAddress.street1);
      expect(updatedAddress.city, 'Dammam');
      expect(updatedAddress.isDefault, false);
      expect(updatedAddress.state, testAddress.state);
    });

    test('should support equality comparison', () {
      const address1 = Address(
        id: 'addr_123',
        street1: '123 King Fahd Road',
        city: 'Riyadh',
        state: 'Riyadh Province',
        postalCode: '12345',
        country: 'Saudi Arabia',
      );

      const address2 = Address(
        id: 'addr_123',
        street1: '123 King Fahd Road',
        city: 'Riyadh',
        state: 'Riyadh Province',
        postalCode: '12345',
        country: 'Saudi Arabia',
      );

      const address3 = Address(
        id: 'addr_456',
        street1: '123 King Fahd Road',
        city: 'Riyadh',
        state: 'Riyadh Province',
        postalCode: '12345',
        country: 'Saudi Arabia',
      );

      expect(address1, equals(address2));
      expect(address1, isNot(equals(address3)));
    });
  });
}
