import 'package:flutter_test/flutter_test.dart';
import 'package:core/core.dart';

void main() {
  group('VendorCategory', () {
    test('should have correct JSON values', () {
      expect(VendorCategory.foodBeverages.name, 'foodBeverages');
      expect(VendorCategory.electronics.name, 'electronics');
      expect(VendorCategory.clothingFashion.name, 'clothingFashion');
    });

    test('should return correct Arabic display names', () {
      expect(VendorCategory.foodBeverages.displayNameAr, 'الطعام والمشروبات');
      expect(VendorCategory.electronics.displayNameAr, 'الإلكترونيات');
      expect(VendorCategory.clothingFashion.displayNameAr, 'الملابس والأزياء');
      expect(VendorCategory.homeGarden.displayNameAr, 'المنزل والحديقة');
      expect(VendorCategory.healthBeauty.displayNameAr, 'الصحة والجمال');
      expect(VendorCategory.booksMedia.displayNameAr, 'الكتب والوسائط');
      expect(VendorCategory.sportsOutdoors.displayNameAr,
          'الرياضة والأنشطة الخارجية');
      expect(VendorCategory.automotive.displayNameAr, 'السيارات');
      expect(VendorCategory.toysGames.displayNameAr, 'الألعاب والترفيه');
      expect(VendorCategory.services.displayNameAr, 'الخدمات');
    });

    test('should return correct icon names', () {
      expect(VendorCategory.foodBeverages.iconName, 'restaurant');
      expect(VendorCategory.electronics.iconName, 'devices');
      expect(VendorCategory.clothingFashion.iconName, 'checkroom');
      expect(VendorCategory.homeGarden.iconName, 'home');
      expect(VendorCategory.healthBeauty.iconName, 'spa');
      expect(VendorCategory.booksMedia.iconName, 'menu_book');
      expect(VendorCategory.sportsOutdoors.iconName, 'sports_soccer');
      expect(VendorCategory.automotive.iconName, 'directions_car');
      expect(VendorCategory.toysGames.iconName, 'toys');
      expect(VendorCategory.services.iconName, 'build');
    });

    test('should parse from string correctly', () {
      expect(VendorCategory.fromString('food_beverages'),
          VendorCategory.foodBeverages);
      expect(
          VendorCategory.fromString('electronics'), VendorCategory.electronics);
      expect(VendorCategory.fromString('CLOTHING_FASHION'),
          VendorCategory.clothingFashion);
    });

    test('should throw error for invalid string', () {
      expect(() => VendorCategory.fromString('invalid'), throwsArgumentError);
    });
  });
}
