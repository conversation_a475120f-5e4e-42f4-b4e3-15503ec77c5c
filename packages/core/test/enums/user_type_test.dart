import 'package:flutter_test/flutter_test.dart';
import 'package:core/core.dart';

void main() {
  group('UserType', () {
    test('should have correct JSON values', () {
      expect(UserType.customer.name, 'customer');
      expect(UserType.vendor.name, 'vendor');
      expect(UserType.courier.name, 'courier');
    });

    test('should return correct Arabic display names', () {
      expect(UserType.customer.displayNameAr, 'عميل');
      expect(UserType.vendor.displayNameAr, 'بائع');
      expect(UserType.courier.displayNameAr, 'مندوب توصيل');
    });

    test('should parse from string correctly', () {
      expect(UserType.fromString('customer'), UserType.customer);
      expect(UserType.fromString('vendor'), UserType.vendor);
      expect(UserType.fromString('courier'), UserType.courier);
      expect(UserType.fromString('CUSTOMER'), UserType.customer);
    });

    test('should throw error for invalid string', () {
      expect(() => UserType.fromString('invalid'), throwsArgumentError);
      expect(() => UserType.fromString(''), throwsArgumentError);
    });
  });
}
