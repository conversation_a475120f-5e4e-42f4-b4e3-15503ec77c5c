import 'package:flutter_test/flutter_test.dart';
import 'package:core/core.dart';

void main() {
  group('OrderStatus', () {
    test('should have correct JSON values', () {
      expect(OrderStatus.pending.name, 'pending');
      expect(OrderStatus.confirmed.name, 'confirmed');
      expect(OrderStatus.preparing.name, 'preparing');
      expect(OrderStatus.readyForPickup.name, 'readyForPickup');
      expect(OrderStatus.pickedUp.name, 'pickedUp');
      expect(OrderStatus.inTransit.name, 'inTransit');
      expect(OrderStatus.delivered.name, 'delivered');
      expect(OrderStatus.cancelled.name, 'cancelled');
    });

    test('should return correct Arabic display names', () {
      expect(OrderStatus.pending.displayNameAr, 'في الانتظار');
      expect(OrderStatus.confirmed.displayNameAr, 'مؤكد');
      expect(OrderStatus.preparing.displayNameAr, 'قيد التحضير');
      expect(OrderStatus.readyForPickup.displayNameAr, 'جاهز للاستلام');
      expect(OrderStatus.pickedUp.displayNameAr, 'تم الاستلام');
      expect(OrderStatus.inTransit.displayNameAr, 'في الطريق');
      expect(OrderStatus.delivered.displayNameAr, 'تم التوصيل');
      expect(OrderStatus.cancelled.displayNameAr, 'ملغي');
    });

    test('should correctly identify cancellable statuses', () {
      expect(OrderStatus.pending.canBeCancelled, isTrue);
      expect(OrderStatus.confirmed.canBeCancelled, isTrue);
      expect(OrderStatus.preparing.canBeCancelled, isTrue);
      expect(OrderStatus.readyForPickup.canBeCancelled, isFalse);
      expect(OrderStatus.pickedUp.canBeCancelled, isFalse);
      expect(OrderStatus.inTransit.canBeCancelled, isFalse);
      expect(OrderStatus.delivered.canBeCancelled, isFalse);
      expect(OrderStatus.cancelled.canBeCancelled, isFalse);
    });

    test('should correctly identify final statuses', () {
      expect(OrderStatus.pending.isFinal, isFalse);
      expect(OrderStatus.confirmed.isFinal, isFalse);
      expect(OrderStatus.preparing.isFinal, isFalse);
      expect(OrderStatus.readyForPickup.isFinal, isFalse);
      expect(OrderStatus.pickedUp.isFinal, isFalse);
      expect(OrderStatus.inTransit.isFinal, isFalse);
      expect(OrderStatus.delivered.isFinal, isTrue);
      expect(OrderStatus.cancelled.isFinal, isTrue);
    });

    test('should parse from string correctly', () {
      expect(OrderStatus.fromString('pending'), OrderStatus.pending);
      expect(OrderStatus.fromString('delivered'), OrderStatus.delivered);
      expect(OrderStatus.fromString('CANCELLED'), OrderStatus.cancelled);
    });

    test('should throw error for invalid string', () {
      expect(() => OrderStatus.fromString('invalid'), throwsArgumentError);
    });
  });
}
