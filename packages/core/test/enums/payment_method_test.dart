import 'package:flutter_test/flutter_test.dart';
import 'package:core/core.dart';

void main() {
  group('PaymentMethod', () {
    test('should have correct JSON values', () {
      expect(PaymentMethod.cashOnDelivery.name, 'cashOnDelivery');
    });

    test('should return correct Arabic display names', () {
      expect(PaymentMethod.cashOnDelivery.displayNameAr, 'الدفع عند الاستلام');
    });

    test('should parse from string correctly', () {
      expect(PaymentMethod.fromString('cash_on_delivery'),
          PaymentMethod.cashOnDelivery);
      expect(PaymentMethod.fromString('CASH_ON_DELIVERY'),
          PaymentMethod.cashOnDelivery);
    });

    test('should throw error for invalid string', () {
      expect(() => PaymentMethod.fromString('invalid'), throwsArgumentError);
      expect(
          () => PaymentMethod.fromString('credit_card'), throwsArgumentError);
    });
  });
}
