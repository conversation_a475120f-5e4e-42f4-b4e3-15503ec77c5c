name: core
description: Core shared package with models, enums, and utilities
version: 1.0.0
publish_to: 'none'

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  
  # Utilities
  json_annotation: ^4.8.1
  equatable: ^2.0.5
  intl: ^0.20.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0
  test: ^1.24.0
  
  # Code Generation
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
  
  # Testing
  mockito: ^5.4.4
flutter:
  uses-material-design: true
  generate: true