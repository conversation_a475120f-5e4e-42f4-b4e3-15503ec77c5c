import 'dart:async';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;
import 'package:core/models/user.dart' as core;
import 'package:core/enums/user_type.dart';
import '../config/supabase_config.dart';
import '../models/auth_result.dart';

/// Repository for handling authentication operations
class AuthRepository {
  final SupabaseClient _client;

  AuthRepository({SupabaseClient? client})
      : _client = client ?? SupabaseConfig.client;

  /// Send magic link to user's email
  Future<AuthResult> sendMagicLink(String email) async {
    try {
      await _client.auth.signInWithOtp(
        email: email,
        emailRedirectTo: 'io.supabase.flutterquickstart://login-callback/',
      );

      return MagicLinkSent(
        email,
        'تم إرسال رابط تسجيل الدخول إلى بريدك الإلكتروني',
      );
    } on AuthException catch (e) {
      return AuthFailure(
        _getLocalizedAuthError(e.message),
        code: e.statusCode,
        exception: e,
      );
    } catch (e) {
      return AuthFailure(
        'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى',
        exception: e is Exception ? e : Exception(e.toString()),
      );
    }
  }

  /// Verify magic link token and authenticate user
  Future<AuthResult> verifyMagicLink(String token) async {
    try {
      final response = await _client.auth.verifyOTP(
        token: token,
        type: OtpType.magiclink,
      );

      if (response.user == null) {
        return const AuthFailure('فشل في التحقق من الرابط');
      }

      final user = await _getUserProfile(response.user!.id);
      if (user == null) {
        // Create new user profile if doesn't exist
        final newUser = await _createUserProfile(response.user!);
        return AuthSuccess(newUser, message: 'تم تسجيل الدخول بنجاح');
      }

      return AuthSuccess(user, message: 'تم تسجيل الدخول بنجاح');
    } on AuthException catch (e) {
      return AuthFailure(
        _getLocalizedAuthError(e.message),
        code: e.statusCode,
        exception: e,
      );
    } catch (e) {
      return AuthFailure(
        'حدث خطأ في التحقق من الرابط',
        exception: e is Exception ? e : Exception(e.toString()),
      );
    }
  }

  /// Get current authenticated user
  Future<core.User?> getCurrentUser() async {
    try {
      final supabaseUser = _client.auth.currentUser;
      if (supabaseUser == null) return null;

      return await _getUserProfile(supabaseUser.id);
    } catch (e) {
      return null;
    }
  }

  /// Sign out current user
  Future<AuthResult> signOut() async {
    try {
      await _client.auth.signOut();
      return AuthSuccess(
        // We need a dummy user for success, but it won't be used
        core.User(
          id: '',
          email: '',
          name: '',
          type: UserType.customer,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        message: 'تم تسجيل الخروج بنجاح',
      );
    } on AuthException catch (e) {
      return AuthFailure(
        _getLocalizedAuthError(e.message),
        code: e.statusCode,
        exception: e,
      );
    } catch (e) {
      return AuthFailure(
        'حدث خطأ في تسجيل الخروج',
        exception: e is Exception ? e : Exception(e.toString()),
      );
    }
  }

  /// Stream of authentication state changes
  Stream<core.User?> get authStateChanges {
    return _client.auth.onAuthStateChange.asyncMap((data) async {
      final supabaseUser = data.session?.user;
      if (supabaseUser == null) return null;

      return await _getUserProfile(supabaseUser.id);
    });
  }

  /// Get user profile from database
  Future<core.User?> _getUserProfile(String userId) async {
    try {
      final response =
          await _client.from('users').select().eq('id', userId).single();

      return core.User.fromJson(response);
    } catch (e) {
      return null;
    }
  }

  /// Create new user profile in database
  Future<core.User> _createUserProfile(supabase.User supabaseUser) async {
    // Get user type from metadata, default to customer
    final userType = supabaseUser.userMetadata?['user_type'] ?? 'customer';

    final userData = {
      'id': supabaseUser.id,
      'email': supabaseUser.email!,
      'name': supabaseUser.userMetadata?['name'] ??
          supabaseUser.email!.split('@').first,
      'user_type': userType,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
    };

    final response =
        await _client.from('users').insert(userData).select().single();

    return core.User.fromJson(response);
  }

  /// Get localized error message
  String _getLocalizedAuthError(String error) {
    switch (error.toLowerCase()) {
      case 'invalid login credentials':
        return 'بيانات تسجيل الدخول غير صحيحة';
      case 'email not confirmed':
        return 'لم يتم تأكيد البريد الإلكتروني';
      case 'user not found':
        return 'المستخدم غير موجود';
      case 'invalid email':
        return 'البريد الإلكتروني غير صحيح';
      case 'signup disabled':
        return 'التسجيل معطل حالياً';
      case 'email rate limit exceeded':
        return 'تم تجاوز الحد المسموح لإرسال الرسائل. يرجى المحاولة لاحقاً';
      default:
        return 'حدث خطأ في المصادقة. يرجى المحاولة مرة أخرى';
    }
  }
}
