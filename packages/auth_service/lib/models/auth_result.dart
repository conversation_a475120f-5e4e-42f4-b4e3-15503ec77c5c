import 'package:core/models/user.dart' as core;

/// Result of authentication operations
sealed class AuthResult {
  const AuthResult();
}

/// Successful authentication result
class AuthSuccess extends AuthResult {
  final core.User user;
  final String? message;

  const AuthSuccess(this.user, {this.message});

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthSuccess &&
        other.user == user &&
        other.message == message;
  }

  @override
  int get hashCode => Object.hash(user, message);
}

/// Failed authentication result
class AuthFailure extends AuthResult {
  final String message;
  final String? code;
  final Exception? exception;

  const AuthFailure(this.message, {this.code, this.exception});

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthFailure &&
        other.message == message &&
        other.code == code;
  }

  @override
  int get hashCode => Object.hash(message, code);
}

/// Magic link sent result
class MagicLinkSent extends AuthResult {
  final String email;
  final String message;

  const MagicLinkSent(this.email, this.message);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MagicLinkSent &&
        other.email == email &&
        other.message == message;
  }

  @override
  int get hashCode => Object.hash(email, message);
}
