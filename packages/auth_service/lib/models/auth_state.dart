import 'package:core/models/user.dart' as core;

/// Represents the current authentication state
class AuthState {
  final core.User? user;
  final bool isLoading;
  final String? error;
  final bool isAuthenticated;

  const AuthState({
    this.user,
    this.isLoading = false,
    this.error,
  }) : isAuthenticated = user != null;

  /// Initial state
  const AuthState.initial() : this();

  /// Loading state
  const AuthState.loading() : this(isLoading: true);

  /// Authenticated state
  const AuthState.authenticated(core.User user) : this(user: user);

  /// Error state
  const AuthState.error(String error) : this(error: error);

  /// Copy with method for state updates
  AuthState copyWith({
    core.User? user,
    bool? isLoading,
    String? error,
  }) {
    return AuthState(
      user: user ?? this.user,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AuthState &&
        other.user == user &&
        other.isLoading == isLoading &&
        other.error == error;
  }

  @override
  int get hashCode => Object.hash(user, isLoading, error);

  @override
  String toString() {
    return 'AuthState(user: $user, isLoading: $isLoading, error: $error)';
  }
}
