import 'package:supabase_flutter/supabase_flutter.dart';

/// Configuration class for Supabase client initialization
class SupabaseConfig {
  static const String _supabaseUrl = String.fromEnvironment(
    'SUPABASE_URL',
    defaultValue: 'https://your-project.supabase.co',
  );

  static const String _supabaseAnonKey = String.fromEnvironment(
    'SUPABASE_ANON_KEY',
    defaultValue: 'your-anon-key',
  );

  /// Initialize Supabase client
  static Future<void> initialize() async {
    await Supabase.initialize(
      url: _supabaseUrl,
      anonKey: _supabaseAnonKey,
      authOptions: const FlutterAuthClientOptions(
        authFlowType: AuthFlowType.pkce,
      ),
    );
  }

  /// Get the initialized Supabase client
  static SupabaseClient get client => Supabase.instance.client;

  /// Get the auth client
  static GoTrueClient get auth => client.auth;
}
