import 'package:flutter/material.dart';
import 'package:supabase_auth_ui/supabase_auth_ui.dart';
import 'package:core/core.dart';

/// Wrapper component for supabase_auth_ui with Arabic localization and Material 3 theming
class AuthUIWrapper extends StatelessWidget {
  /// The user type for this authentication flow
  final UserType userType;

  /// Callback when authentication is successful
  final VoidCallback? onSignInComplete;

  /// Callback when user wants to navigate back
  final VoidCallback? onBackPressed;

  /// Additional metadata to include during registration
  final Map<String, dynamic>? additionalMetadata;

  const AuthUIWrapper({
    super.key,
    required this.userType,
    this.onSignInComplete,
    this.onBackPressed,
    this.additionalMetadata,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(_getAppBarTitle()),
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        leading: onBackPressed != null
            ? IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: onBackPressed,
              )
            : null,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SupaEmailAuth(
          redirectTo: null,
          onSignInComplete: (response) {
            _handleSignInComplete(response);
          },
          onSignUpComplete: (response) {
            _handleSignUpComplete(response);
          },
          localization: _buildArabicLocalization(),
        ),
      ),
    );
  }

  /// Get the appropriate app bar title based on user type
  String _getAppBarTitle() {
    switch (userType) {
      case UserType.customer:
        return 'تسجيل دخول العملاء';
      case UserType.vendor:
        return 'تسجيل دخول البائعين';
      case UserType.courier:
        return 'تسجيل دخول المندوبين';
    }
  }

  /// Handle successful sign in
  void _handleSignInComplete(AuthResponse response) {
    if (response.user != null) {
      onSignInComplete?.call();
    }
  }

  /// Handle successful sign up
  void _handleSignUpComplete(AuthResponse response) {
    if (response.user != null) {
      onSignInComplete?.call();
    }
  }

  /// Build Arabic localization for supabase_auth_ui
  SupaEmailAuthLocalization _buildArabicLocalization() {
    return const SupaEmailAuthLocalization(
      enterEmail: 'أدخل بريدك الإلكتروني',
      enterPassword: 'أدخل كلمة المرور',
      forgotPassword: 'نسيت كلمة المرور؟',
      signIn: 'تسجيل الدخول',
      signUp: 'إنشاء حساب جديد',
      dontHaveAccount: 'ليس لديك حساب؟',
      unexpectedError: 'حدث خطأ غير متوقع',
    );
  }
}
