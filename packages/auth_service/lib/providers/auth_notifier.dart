import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:core/models/user.dart' as core;
import '../models/auth_state.dart';
import '../models/auth_result.dart';
import '../repositories/auth_repository.dart';

/// Notifier for managing authentication state
class AuthNotifier extends StateNotifier<AuthState> {
  final AuthRepository _authRepository;
  StreamSubscription<core.User?>? _authSubscription;

  AuthNotifier(this._authRepository) : super(const AuthState.initial()) {
    _initializeAuth();
  }

  /// Initialize authentication state and listen to changes
  void _initializeAuth() {
    state = const AuthState.loading();

    _authSubscription = _authRepository.authStateChanges.listen(
      (user) {
        if (user != null) {
          state = AuthState.authenticated(user);
        } else {
          state = const AuthState.initial();
        }
      },
      onError: (error) {
        state = AuthState.error(error.toString());
      },
    );

    // Check current user on initialization
    _checkCurrentUser();
  }

  /// Check if user is currently authenticated
  Future<void> _checkCurrentUser() async {
    try {
      final user = await _authRepository.getCurrentUser();
      if (user != null) {
        state = AuthState.authenticated(user);
      } else {
        state = const AuthState.initial();
      }
    } catch (e) {
      state = AuthState.error(e.toString());
    }
  }

  /// Send magic link to user's email
  Future<AuthResult> sendMagicLink(String email) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await _authRepository.sendMagicLink(email);

      if (result is AuthFailure) {
        state = state.copyWith(isLoading: false, error: result.message);
      } else {
        state = state.copyWith(isLoading: false);
      }

      return result;
    } catch (e) {
      final error = 'حدث خطأ غير متوقع';
      state = state.copyWith(isLoading: false, error: error);
      return AuthFailure(error,
          exception: e is Exception ? e : Exception(e.toString()));
    }
  }

  /// Verify magic link token
  Future<AuthResult> verifyMagicLink(String token) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await _authRepository.verifyMagicLink(token);

      if (result is AuthSuccess) {
        state = AuthState.authenticated(result.user);
      } else if (result is AuthFailure) {
        state = state.copyWith(isLoading: false, error: result.message);
      }

      return result;
    } catch (e) {
      final error = 'حدث خطأ في التحقق من الرابط';
      state = state.copyWith(isLoading: false, error: error);
      return AuthFailure(error,
          exception: e is Exception ? e : Exception(e.toString()));
    }
  }

  /// Sign out current user
  Future<AuthResult> signOut() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await _authRepository.signOut();

      if (result is AuthSuccess) {
        state = const AuthState.initial();
      } else if (result is AuthFailure) {
        state = state.copyWith(isLoading: false, error: result.message);
      }

      return result;
    } catch (e) {
      final error = 'حدث خطأ في تسجيل الخروج';
      state = state.copyWith(isLoading: false, error: error);
      return AuthFailure(error,
          exception: e is Exception ? e : Exception(e.toString()));
    }
  }

  /// Clear any error state
  void clearError() {
    if (state.error != null) {
      state = state.copyWith(error: null);
    }
  }

  /// Refresh current user data
  Future<void> refreshUser() async {
    if (state.user != null) {
      state = state.copyWith(isLoading: true);
      await _checkCurrentUser();
    }
  }

  @override
  void dispose() {
    _authSubscription?.cancel();
    super.dispose();
  }
}
