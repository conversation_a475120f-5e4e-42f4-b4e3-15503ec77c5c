import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:core/models/user.dart';
import 'package:core/enums/user_type.dart';
import 'package:auth_service/providers/auth_providers.dart';
import 'package:auth_service/repositories/auth_repository.dart';
import 'package:auth_service/models/auth_state.dart';

import 'auth_providers_test.mocks.dart';

@GenerateMocks([AuthRepository])
void main() {
  group('AuthProviders', () {
    late ProviderContainer container;
    late MockAuthRepository mockRepository;

    final testUser = User(
      id: 'test-id',
      email: '<EMAIL>',
      name: 'Test User',
      type: UserType.customer,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    setUp(() {
      mockRepository = MockAuthRepository();

      // Mock the auth state changes stream
      when(mockRepository.authStateChanges)
          .thenAnswer((_) => Stream.value(null));

      // Mock getCurrentUser to return null initially
      when(mockRepository.getCurrentUser()).thenAnswer((_) async => null);

      container = ProviderContainer(
        overrides: [
          authRepositoryProvider.overrideWithValue(mockRepository),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    test('authRepositoryProvider should provide AuthRepository', () {
      final repository = container.read(authRepositoryProvider);
      expect(repository, isA<AuthRepository>());
    });

    test('authStateProvider should provide initial state', () {
      final state = container.read(authStateProvider);
      expect(state, isA<AuthState>());
    });

    test('currentUserProvider should return null initially', () {
      final user = container.read(currentUserProvider);
      expect(user, isNull);
    });

    test('currentUserProvider should return user when authenticated', () {
      // Update the auth state to authenticated
      final notifier = container.read(authStateProvider.notifier);
      notifier.state = AuthState.authenticated(testUser);

      final user = container.read(currentUserProvider);
      expect(user, equals(testUser));
    });

    test('authLoadingProvider should return loading state', () {
      final isLoading = container.read(authLoadingProvider);
      expect(isLoading, isA<bool>());
    });

    test('authLoadingProvider should return true when loading', () {
      final notifier = container.read(authStateProvider.notifier);
      notifier.state = const AuthState.loading();

      final isLoading = container.read(authLoadingProvider);
      expect(isLoading, isTrue);
    });

    test('authErrorProvider should return null initially', () {
      final error = container.read(authErrorProvider);
      expect(error, isNull);
    });

    test('authErrorProvider should return error message when error occurs', () {
      const errorMessage = 'Test error';
      final notifier = container.read(authStateProvider.notifier);
      notifier.state = const AuthState.error(errorMessage);

      final error = container.read(authErrorProvider);
      expect(error, equals(errorMessage));
    });

    test('isAuthenticatedProvider should return false initially', () {
      final isAuthenticated = container.read(isAuthenticatedProvider);
      expect(isAuthenticated, isFalse);
    });

    test('isAuthenticatedProvider should return true when authenticated', () {
      final notifier = container.read(authStateProvider.notifier);
      notifier.state = AuthState.authenticated(testUser);

      final isAuthenticated = container.read(isAuthenticatedProvider);
      expect(isAuthenticated, isTrue);
    });

    test('providers should react to state changes', () {
      final notifier = container.read(authStateProvider.notifier);

      // Initially not authenticated
      expect(container.read(isAuthenticatedProvider), isFalse);
      expect(container.read(currentUserProvider), isNull);
      expect(container.read(authLoadingProvider), isFalse);
      expect(container.read(authErrorProvider), isNull);

      // Set loading state
      notifier.state = const AuthState.loading();
      expect(container.read(authLoadingProvider), isTrue);
      expect(container.read(isAuthenticatedProvider), isFalse);

      // Set authenticated state
      notifier.state = AuthState.authenticated(testUser);
      expect(container.read(isAuthenticatedProvider), isTrue);
      expect(container.read(currentUserProvider), equals(testUser));
      expect(container.read(authLoadingProvider), isFalse);

      // Set error state
      const errorMessage = 'Test error';
      notifier.state = const AuthState.error(errorMessage);
      expect(container.read(authErrorProvider), equals(errorMessage));
      expect(container.read(isAuthenticatedProvider), isFalse);
    });
  });
}
