import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:core/models/user.dart';
import 'package:core/enums/user_type.dart';
import 'package:auth_service/providers/auth_notifier.dart';
import 'package:auth_service/repositories/auth_repository.dart';
import 'package:auth_service/models/auth_state.dart';
import 'package:auth_service/models/auth_result.dart';

import 'auth_notifier_test.mocks.dart';

@GenerateMocks([AuthRepository])
void main() {
  group('AuthNotifier', () {
    late MockAuthRepository mockRepository;
    late AuthNotifier notifier;

    final testUser = User(
      id: 'test-id',
      email: '<EMAIL>',
      name: 'Test User',
      type: UserType.customer,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    setUp(() {
      mockRepository = MockAuthRepository();

      // Mock the auth state changes stream
      when(mockRepository.authStateChanges)
          .thenAnswer((_) => Stream.value(null));

      // Mock getCurrentUser to return null initially
      when(mockRepository.getCurrentUser()).thenAnswer((_) async => null);

      notifier = AuthNotifier(mockRepository);
    });

    tearDown(() {
      notifier.dispose();
    });

    test('should initialize with loading state', () {
      expect(notifier.state.isLoading, isTrue);
    });

    group('sendMagicLink', () {
      test('should update state to loading and return success', () async {
        const email = '<EMAIL>';
        final expectedResult = MagicLinkSent(email, 'Success message');

        when(mockRepository.sendMagicLink(email))
            .thenAnswer((_) async => expectedResult);

        final result = await notifier.sendMagicLink(email);

        expect(result, equals(expectedResult));
        expect(notifier.state.isLoading, isFalse);
        expect(notifier.state.error, isNull);
      });

      test('should update state with error on failure', () async {
        const email = '<EMAIL>';
        const errorMessage = 'Error message';
        const expectedResult = AuthFailure(errorMessage);

        when(mockRepository.sendMagicLink(email))
            .thenAnswer((_) async => expectedResult);

        final result = await notifier.sendMagicLink(email);

        expect(result, equals(expectedResult));
        expect(notifier.state.isLoading, isFalse);
        expect(notifier.state.error, equals(errorMessage));
      });

      test('should handle exceptions', () async {
        const email = '<EMAIL>';
        final exception = Exception('Network error');

        when(mockRepository.sendMagicLink(email)).thenThrow(exception);

        final result = await notifier.sendMagicLink(email);

        expect(result, isA<AuthFailure>());
        expect(notifier.state.isLoading, isFalse);
        expect(notifier.state.error, isNotNull);
      });
    });

    group('verifyMagicLink', () {
      test('should authenticate user on success', () async {
        const token = 'test-token';
        final expectedResult = AuthSuccess(testUser, message: 'Success');

        when(mockRepository.verifyMagicLink(token))
            .thenAnswer((_) async => expectedResult);

        final result = await notifier.verifyMagicLink(token);

        expect(result, equals(expectedResult));
        expect(notifier.state.isAuthenticated, isTrue);
        expect(notifier.state.user, equals(testUser));
        expect(notifier.state.error, isNull);
      });

      test('should update state with error on failure', () async {
        const token = 'test-token';
        const errorMessage = 'Invalid token';
        const expectedResult = AuthFailure(errorMessage);

        when(mockRepository.verifyMagicLink(token))
            .thenAnswer((_) async => expectedResult);

        final result = await notifier.verifyMagicLink(token);

        expect(result, equals(expectedResult));
        expect(notifier.state.isAuthenticated, isFalse);
        expect(notifier.state.error, equals(errorMessage));
      });

      test('should handle exceptions', () async {
        const token = 'test-token';
        final exception = Exception('Network error');

        when(mockRepository.verifyMagicLink(token)).thenThrow(exception);

        final result = await notifier.verifyMagicLink(token);

        expect(result, isA<AuthFailure>());
        expect(notifier.state.isLoading, isFalse);
        expect(notifier.state.error, isNotNull);
      });
    });

    group('signOut', () {
      test('should clear user state on success', () async {
        // First set authenticated state
        notifier.state = AuthState.authenticated(testUser);

        final expectedResult = AuthSuccess(testUser, message: 'Signed out');
        when(mockRepository.signOut()).thenAnswer((_) async => expectedResult);

        final result = await notifier.signOut();

        expect(result, equals(expectedResult));
        expect(notifier.state.isAuthenticated, isFalse);
        expect(notifier.state.user, isNull);
      });

      test('should update state with error on failure', () async {
        const errorMessage = 'Sign out failed';
        const expectedResult = AuthFailure(errorMessage);

        when(mockRepository.signOut()).thenAnswer((_) async => expectedResult);

        final result = await notifier.signOut();

        expect(result, equals(expectedResult));
        expect(notifier.state.error, equals(errorMessage));
      });

      test('should handle exceptions', () async {
        final exception = Exception('Network error');

        when(mockRepository.signOut()).thenThrow(exception);

        final result = await notifier.signOut();

        expect(result, isA<AuthFailure>());
        expect(notifier.state.error, isNotNull);
      });
    });

    group('clearError', () {
      test('should clear error from state', () {
        notifier.state = const AuthState.error('Test error');

        notifier.clearError();

        expect(notifier.state.error, isNull);
      });

      test('should not change state if no error', () {
        final initialState = notifier.state;

        notifier.clearError();

        expect(notifier.state, equals(initialState));
      });
    });

    group('refreshUser', () {
      test('should refresh user data when authenticated', () async {
        notifier.state = AuthState.authenticated(testUser);

        when(mockRepository.getCurrentUser()).thenAnswer((_) async => testUser);

        await notifier.refreshUser();

        verify(mockRepository.getCurrentUser()).called(1);
      });

      test('should not refresh when not authenticated', () async {
        notifier.state = const AuthState.initial();

        await notifier.refreshUser();

        verifyNever(mockRepository.getCurrentUser());
      });
    });
  });
}
