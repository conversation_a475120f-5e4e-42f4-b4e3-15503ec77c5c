import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:auth_service/ui/auth_ui_wrapper.dart';
import 'package:core/core.dart';

void main() {
  group('AuthUIWrapper', () {
    Widget createTestWidget({
      required UserType userType,
      VoidCallback? onSignInComplete,
      VoidCallback? onBackPressed,
      Map<String, dynamic>? additionalMetadata,
    }) {
      return MaterialApp(
        home: AuthUIWrapper(
          userType: userType,
          onSignInComplete: onSignInComplete,
          onBackPressed: onBackPressed,
          additionalMetadata: additionalMetadata,
        ),
      );
    }

    testWidgets('should display correct app bar title for customer', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(userType: UserType.customer));
      await tester.pumpAndSettle();

      expect(find.text('تسجيل دخول العملاء'), findsOneWidget);
    });

    testWidgets('should display correct app bar title for vendor', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(userType: UserType.vendor));
      await tester.pumpAndSettle();

      expect(find.text('تسجيل دخول البائعين'), findsOneWidget);
    });

    testWidgets('should display correct app bar title for courier', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(userType: UserType.courier));
      await tester.pumpAndSettle();

      expect(find.text('تسجيل دخول المندوبين'), findsOneWidget);
    });

    testWidgets('should show back button when onBackPressed is provided', (WidgetTester tester) async {
      bool backPressed = false;
      
      await tester.pumpWidget(createTestWidget(
        userType: UserType.customer,
        onBackPressed: () => backPressed = true,
      ));
      await tester.pumpAndSettle();

      // Should show back button
      expect(find.byIcon(Icons.arrow_back), findsOneWidget);
      
      // Tap back button
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();
      
      expect(backPressed, isTrue);
    });

    testWidgets('should not show back button when onBackPressed is null', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(
        userType: UserType.customer,
        onBackPressed: null,
      ));
      await tester.pumpAndSettle();

      // Should not show back button
      expect(find.byIcon(Icons.arrow_back), findsNothing);
    });

    testWidgets('should include user type in metadata', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(userType: UserType.vendor));
      await tester.pumpAndSettle();

      // Note: We can't directly test the metadata without mocking Supabase,
      // but we can verify the widget builds correctly
      expect(find.byType(AuthUIWrapper), findsOneWidget);
    });

    testWidgets('should include additional metadata when provided', (WidgetTester tester) async {
      final additionalMetadata = {
        'business_name': 'Test Business',
        'category': 'electronics',
      };

      await tester.pumpWidget(createTestWidget(
        userType: UserType.vendor,
        additionalMetadata: additionalMetadata,
      ));
      await tester.pumpAndSettle();

      // Verify widget builds correctly with additional metadata
      expect(find.byType(AuthUIWrapper), findsOneWidget);
    });

    testWidgets('should handle sign in completion callback', (WidgetTester tester) async {
      bool signInCompleted = false;
      
      await tester.pumpWidget(createTestWidget(
        userType: UserType.customer,
        onSignInComplete: () => signInCompleted = true,
      ));
      await tester.pumpAndSettle();

      // Note: We can't directly trigger the callback without mocking Supabase,
      // but we can verify the widget builds correctly
      expect(find.byType(AuthUIWrapper), findsOneWidget);
    });

    group('_getAppBarTitle', () {
      testWidgets('should return correct title for each user type', (WidgetTester tester) async {
        // Test customer title
        await tester.pumpWidget(createTestWidget(userType: UserType.customer));
        await tester.pumpAndSettle();
        expect(find.text('تسجيل دخول العملاء'), findsOneWidget);

        // Test vendor title
        await tester.pumpWidget(createTestWidget(userType: UserType.vendor));
        await tester.pumpAndSettle();
        expect(find.text('تسجيل دخول البائعين'), findsOneWidget);

        // Test courier title
        await tester.pumpWidget(createTestWidget(userType: UserType.courier));
        await tester.pumpAndSettle();
        expect(find.text('تسجيل دخول المندوبين'), findsOneWidget);
      });
    });

    group('Material 3 theming', () {
      testWidgets('should apply Material 3 theme', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: ThemeData(
              useMaterial3: true,
              colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
            ),
            home: AuthUIWrapper(userType: UserType.customer),
          ),
        );
        await tester.pumpAndSettle();

        // Verify the widget builds with Material 3 theme
        expect(find.byType(AuthUIWrapper), findsOneWidget);
        expect(find.byType(AppBar), findsOneWidget);
      });
    });

    group('Arabic localization', () {
      testWidgets('should display Arabic text correctly', (WidgetTester tester) async {
        await tester.pumpWidget(createTestWidget(userType: UserType.customer));
        await tester.pumpAndSettle();

        // Verify Arabic text is displayed
        expect(find.text('تسجيل دخول العملاء'), findsOneWidget);
        
        // Note: More detailed localization testing would require
        // mocking the SupaEmailAuth component
      });
    });
  });
}
