import 'package:flutter_test/flutter_test.dart';
import 'package:core/models/user.dart';
import 'package:core/enums/user_type.dart';
import 'package:auth_service/models/auth_state.dart';

void main() {
  group('AuthState', () {
    final testUser = User(
      id: 'test-id',
      email: '<EMAIL>',
      name: 'Test User',
      type: UserType.customer,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    test('should create initial state correctly', () {
      const state = AuthState.initial();

      expect(state.user, isNull);
      expect(state.isLoading, isFalse);
      expect(state.error, isNull);
      expect(state.isAuthenticated, isFalse);
    });

    test('should create loading state correctly', () {
      const state = AuthState.loading();

      expect(state.user, isNull);
      expect(state.isLoading, isTrue);
      expect(state.error, isNull);
      expect(state.isAuthenticated, isFalse);
    });

    test('should create authenticated state correctly', () {
      final state = AuthState.authenticated(testUser);

      expect(state.user, equals(testUser));
      expect(state.isLoading, isFalse);
      expect(state.error, isNull);
      expect(state.isAuthenticated, isTrue);
    });

    test('should create error state correctly', () {
      const errorMessage = 'Test error';
      const state = AuthState.error(errorMessage);

      expect(state.user, isNull);
      expect(state.isLoading, isFalse);
      expect(state.error, equals(errorMessage));
      expect(state.isAuthenticated, isFalse);
    });

    test('should copy with new values correctly', () {
      const initialState = AuthState.initial();
      const errorMessage = 'Test error';

      final newState = initialState.copyWith(
        isLoading: true,
        error: errorMessage,
      );

      expect(newState.user, isNull);
      expect(newState.isLoading, isTrue);
      expect(newState.error, equals(errorMessage));
      expect(newState.isAuthenticated, isFalse);
    });

    test('should maintain equality correctly', () {
      const state1 = AuthState.initial();
      const state2 = AuthState.initial();
      final state3 = AuthState.authenticated(testUser);
      final state4 = AuthState.authenticated(testUser);

      expect(state1, equals(state2));
      expect(state3, equals(state4));
      expect(state1, isNot(equals(state3)));
    });

    test('should have correct toString representation', () {
      const state = AuthState.loading();
      final toString = state.toString();

      expect(toString, contains('AuthState'));
      expect(toString, contains('isLoading: true'));
    });
  });
}
