import 'package:flutter_test/flutter_test.dart';
import 'package:core/models/user.dart';
import 'package:core/enums/user_type.dart';
import 'package:auth_service/models/auth_result.dart';

void main() {
  group('AuthResult', () {
    final testUser = User(
      id: 'test-id',
      email: '<EMAIL>',
      name: 'Test User',
      type: UserType.customer,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    group('AuthSuccess', () {
      test('should create success result correctly', () {
        const message = 'Success message';
        final result = AuthSuccess(testUser, message: message);

        expect(result.user, equals(testUser));
        expect(result.message, equals(message));
      });

      test('should create success result without message', () {
        final result = AuthSuccess(testUser);

        expect(result.user, equals(testUser));
        expect(result.message, isNull);
      });

      test('should maintain equality correctly', () {
        final result1 = AuthSuccess(testUser, message: 'test');
        final result2 = AuthSuccess(testUser, message: 'test');
        final result3 = AuthSuccess(testUser, message: 'different');

        expect(result1, equals(result2));
        expect(result1, isNot(equals(result3)));
      });
    });

    group('AuthFailure', () {
      test('should create failure result correctly', () {
        const message = 'Error message';
        const code = 'ERROR_CODE';
        final exception = Exception('Test exception');

        final result = AuthFailure(
          message,
          code: code,
          exception: exception,
        );

        expect(result.message, equals(message));
        expect(result.code, equals(code));
        expect(result.exception, equals(exception));
      });

      test('should create failure result with minimal data', () {
        const message = 'Error message';
        const result = AuthFailure(message);

        expect(result.message, equals(message));
        expect(result.code, isNull);
        expect(result.exception, isNull);
      });

      test('should maintain equality correctly', () {
        const result1 = AuthFailure('error', code: 'CODE');
        const result2 = AuthFailure('error', code: 'CODE');
        const result3 = AuthFailure('error', code: 'DIFFERENT');

        expect(result1, equals(result2));
        expect(result1, isNot(equals(result3)));
      });
    });

    group('MagicLinkSent', () {
      test('should create magic link sent result correctly', () {
        const email = '<EMAIL>';
        const message = 'Magic link sent';

        const result = MagicLinkSent(email, message);

        expect(result.email, equals(email));
        expect(result.message, equals(message));
      });

      test('should maintain equality correctly', () {
        const result1 = MagicLinkSent('<EMAIL>', 'message');
        const result2 = MagicLinkSent('<EMAIL>', 'message');
        const result3 = MagicLinkSent('<EMAIL>', 'message');

        expect(result1, equals(result2));
        expect(result1, isNot(equals(result3)));
      });
    });
  });
}
