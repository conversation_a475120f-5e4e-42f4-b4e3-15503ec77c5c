import 'package:flutter_test/flutter_test.dart';
import 'package:auth_service/models/auth_result.dart';

void main() {
  group('AuthRepository', () {
    test('should have proper result types defined', () {
      // Test that result types are properly defined
      const failure = AuthFailure('Test error');
      const magicLinkSent = MagicLinkSent('<EMAIL>', 'Sent');

      expect(failure, isA<AuthResult>());
      expect(magicLinkSent, isA<AuthResult>());
      expect(failure.message, equals('Test error'));
      expect(magicLinkSent.email, equals('<EMAIL>'));
    });
  });
}
