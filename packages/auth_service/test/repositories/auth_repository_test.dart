import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:supabase_flutter/supabase_flutter.dart' as supabase;
import 'package:core/models/user.dart';
import 'package:core/enums/user_type.dart';
import 'package:auth_service/repositories/auth_repository.dart';
import 'package:auth_service/models/auth_result.dart';

import 'auth_repository_test.mocks.dart';

@GenerateMocks([
  SupabaseClient,
  GoTrueClient,
  SupabaseQueryBuilder,
  PostgrestFilterBuilder,
  PostgrestBuilder,
  AuthResponse,
], customMocks: [
  MockSpec<supabase.User>(as: #MockSupabaseUser),
])
void main() {
  group('AuthRepository', () {
    late MockSupabaseClient mockClient;
    late MockGoTrueClient mockAuth;
    late AuthRepository repository;

    setUp(() {
      mockClient = MockSupabaseClient();
      mockAuth = MockGoTrueClient();
      repository = AuthRepository(client: mockClient);

      when(mockClient.auth).thenReturn(mockAuth);
    });

    group('sendMagicLink', () {
      test('should return MagicLinkSent on success', () async {
        const email = '<EMAIL>';

        when(mockAuth.signInWithOtp(
          email: email,
          emailRedirectTo: anyNamed('emailRedirectTo'),
        )).thenAnswer((_) async => {});

        final result = await repository.sendMagicLink(email);

        expect(result, isA<MagicLinkSent>());
        final magicLinkResult = result as MagicLinkSent;
        expect(magicLinkResult.email, equals(email));
        expect(magicLinkResult.message, contains('تم إرسال رابط'));
      });

      test('should return AuthFailure on AuthException', () async {
        const email = '<EMAIL>';
        final authException = AuthException('Invalid email');

        when(mockAuth.signInWithOtp(
          email: email,
          emailRedirectTo: anyNamed('emailRedirectTo'),
        )).thenThrow(authException);

        final result = await repository.sendMagicLink(email);

        expect(result, isA<AuthFailure>());
        final failureResult = result as AuthFailure;
        expect(failureResult.message, contains('البريد الإلكتروني غير صحيح'));
        expect(failureResult.exception, equals(authException));
      });

      test('should return AuthFailure on general exception', () async {
        const email = '<EMAIL>';
        final exception = Exception('Network error');

        when(mockAuth.signInWithOtp(
          email: email,
          emailRedirectTo: anyNamed('emailRedirectTo'),
        )).thenThrow(exception);

        final result = await repository.sendMagicLink(email);

        expect(result, isA<AuthFailure>());
        final failureResult = result as AuthFailure;
        expect(failureResult.message, contains('حدث خطأ غير متوقع'));
        expect(failureResult.exception, equals(exception));
      });
    });

    group('verifyMagicLink', () {
      test('should return AuthSuccess with existing user', () async {
        const token = 'test-token';
        final mockUser = MockSupabaseUser();
        final mockAuthResponse = MockAuthResponse();
        final userData = {
          'id': 'user-id',
          'email': '<EMAIL>',
          'name': 'Test User',
          'type': 'customer',
          'createdAt': DateTime.now().toIso8601String(),
          'updatedAt': DateTime.now().toIso8601String(),
        };

        when(mockUser.id).thenReturn('user-id');
        when(mockAuthResponse.user).thenReturn(mockUser);
        when(mockAuth.verifyOTP(
          token: token,
          type: OtpType.magiclink,
        )).thenAnswer((_) async => mockAuthResponse);

        final mockQueryBuilder = MockSupabaseQueryBuilder();
        final mockFilterBuilder = MockPostgrestFilterBuilder();
        final mockBuilder = MockPostgrestBuilder();

        when(mockClient.from('users')).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.eq('id', 'user-id')).thenReturn(mockBuilder);
        when(mockBuilder.single()).thenAnswer((_) async => userData);

        final result = await repository.verifyMagicLink(token);

        expect(result, isA<AuthSuccess>());
        final successResult = result as AuthSuccess;
        expect(successResult.user.id, equals('user-id'));
        expect(successResult.message, contains('تم تسجيل الدخول بنجاح'));
      });

      test('should return AuthFailure when user is null', () async {
        const token = 'test-token';
        final mockAuthResponse = MockAuthResponse();

        when(mockAuthResponse.user).thenReturn(null);
        when(mockAuth.verifyOTP(
          token: token,
          type: OtpType.magiclink,
        )).thenAnswer((_) async => mockAuthResponse);

        final result = await repository.verifyMagicLink(token);

        expect(result, isA<AuthFailure>());
        final failureResult = result as AuthFailure;
        expect(failureResult.message, contains('فشل في التحقق من الرابط'));
      });

      test('should return AuthFailure on AuthException', () async {
        const token = 'test-token';
        final authException = AuthException('Invalid token');

        when(mockAuth.verifyOTP(
          token: token,
          type: OtpType.magiclink,
        )).thenThrow(authException);

        final result = await repository.verifyMagicLink(token);

        expect(result, isA<AuthFailure>());
        final failureResult = result as AuthFailure;
        expect(failureResult.exception, equals(authException));
      });
    });

    group('getCurrentUser', () {
      test('should return user when authenticated', () async {
        final mockUser = MockSupabaseUser();
        final userData = {
          'id': 'user-id',
          'email': '<EMAIL>',
          'name': 'Test User',
          'type': 'customer',
          'createdAt': DateTime.now().toIso8601String(),
          'updatedAt': DateTime.now().toIso8601String(),
        };

        when(mockUser.id).thenReturn('user-id');
        when(mockAuth.currentUser).thenReturn(mockUser);

        final mockQueryBuilder = MockSupabaseQueryBuilder();
        final mockFilterBuilder = MockPostgrestFilterBuilder();
        final mockBuilder = MockPostgrestBuilder();

        when(mockClient.from('users')).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenReturn(mockFilterBuilder);
        when(mockFilterBuilder.eq('id', 'user-id')).thenReturn(mockBuilder);
        when(mockBuilder.single()).thenAnswer((_) async => userData);

        final result = await repository.getCurrentUser();

        expect(result, isNotNull);
        expect(result!.id, equals('user-id'));
      });

      test('should return null when not authenticated', () async {
        when(mockAuth.currentUser).thenReturn(null);

        final result = await repository.getCurrentUser();

        expect(result, isNull);
      });

      test('should return null on exception', () async {
        final mockUser = MockSupabaseUser();
        when(mockUser.id).thenReturn('user-id');
        when(mockAuth.currentUser).thenReturn(mockUser);

        final mockQueryBuilder = MockSupabaseQueryBuilder();
        when(mockClient.from('users')).thenReturn(mockQueryBuilder);
        when(mockQueryBuilder.select()).thenThrow(Exception('Database error'));

        final result = await repository.getCurrentUser();

        expect(result, isNull);
      });
    });

    group('signOut', () {
      test('should return AuthSuccess on successful sign out', () async {
        when(mockAuth.signOut()).thenAnswer((_) async => {});

        final result = await repository.signOut();

        expect(result, isA<AuthSuccess>());
        final successResult = result as AuthSuccess;
        expect(successResult.message, contains('تم تسجيل الخروج بنجاح'));
      });

      test('should return AuthFailure on AuthException', () async {
        final authException = AuthException('Sign out failed');
        when(mockAuth.signOut()).thenThrow(authException);

        final result = await repository.signOut();

        expect(result, isA<AuthFailure>());
        final failureResult = result as AuthFailure;
        expect(failureResult.exception, equals(authException));
      });

      test('should return AuthFailure on general exception', () async {
        final exception = Exception('Network error');
        when(mockAuth.signOut()).thenThrow(exception);

        final result = await repository.signOut();

        expect(result, isA<AuthFailure>());
        final failureResult = result as AuthFailure;
        expect(failureResult.message, contains('حدث خطأ في تسجيل الخروج'));
        expect(failureResult.exception, equals(exception));
      });
    });
  });
}
