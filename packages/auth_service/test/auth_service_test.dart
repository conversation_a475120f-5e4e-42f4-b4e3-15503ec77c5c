import 'package:flutter_test/flutter_test.dart';
import 'package:auth_service/auth_service.dart';

void main() {
  group('AuthService', () {
    test('should export all required classes', () {
      // Test that all exports are available
      expect(SupabaseConfig, isNotNull);
      expect(AuthState, isNotNull);
      expect(AuthResult, isNotNull);
      expect(AuthRepository, isNotNull);
      expect(AuthNotifier, isNotNull);
    });
  });
}
