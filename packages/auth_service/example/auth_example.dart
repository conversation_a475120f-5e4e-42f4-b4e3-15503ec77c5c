import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:auth_service/auth_service.dart';

/// Example of how to use the authentication service
class AuthExample extends ConsumerWidget {
  const AuthExample({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authStateProvider);
    final authNotifier = ref.read(authStateProvider.notifier);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Auth Example'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Show current auth state
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Authentication Status',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text('Authenticated: ${authState.isAuthenticated}'),
                    Text('Loading: ${authState.isLoading}'),
                    if (authState.user != null)
                      Text('User: ${authState.user!.email}'),
                    if (authState.error != null)
                      Text(
                        'Error: ${authState.error}',
                        style: const TextStyle(color: Colors.red),
                      ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Authentication actions
            if (!authState.isAuthenticated) ...[
              const TextField(
                decoration: InputDecoration(
                  labelText: 'Email',
                  hintText: 'Enter your email',
                ),
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: authState.isLoading
                    ? null
                    : () async {
                        final result = await authNotifier.sendMagicLink(
                          '<EMAIL>',
                        );

                        if (result is MagicLinkSent) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text(result.message)),
                          );
                        } else if (result is AuthFailure) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(result.message),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      },
                child: authState.isLoading
                    ? const CircularProgressIndicator()
                    : const Text('Send Magic Link'),
              ),
            ] else ...[
              ElevatedButton(
                onPressed: authState.isLoading
                    ? null
                    : () async {
                        final result = await authNotifier.signOut();

                        if (result is AuthSuccess) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                                content: Text(result.message ?? 'Signed out')),
                          );
                        } else if (result is AuthFailure) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text(result.message),
                              backgroundColor: Colors.red,
                            ),
                          );
                        }
                      },
                child: const Text('Sign Out'),
              ),
            ],

            const SizedBox(height: 16),

            // Clear error button
            if (authState.error != null)
              ElevatedButton(
                onPressed: () => authNotifier.clearError(),
                child: const Text('Clear Error'),
              ),
          ],
        ),
      ),
    );
  }
}

/// Example app that demonstrates the authentication service
class AuthExampleApp extends StatelessWidget {
  const AuthExampleApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ProviderScope(
      child: MaterialApp(
        title: 'Auth Service Example',
        theme: ThemeData(
          primarySwatch: Colors.blue,
        ),
        home: const AuthExample(),
      ),
    );
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Supabase (you would need to provide real credentials)
  await SupabaseConfig.initialize();

  runApp(const AuthExampleApp());
}
