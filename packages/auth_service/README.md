# Authentication Service Package

A comprehensive Flutter package for handling authentication using Supabase with magic link authentication, built with Riverpod state management and Arabic localization support.

## Features

- 🔐 Magic link authentication with Supabase
- 🌍 Arabic localization with RTL support
- 🔄 Reactive state management with Riverpod
- 📱 Comprehensive error handling
- 🧪 Test-driven development approach
- 🎯 Type-safe authentication results

## Getting Started

### Installation

Add this package to your `pubspec.yaml`:

```yaml
dependencies:
  auth_service:
    path: ../packages/auth_service
```

### Setup

1. Initialize Supabase in your app:

```dart
import 'package:auth_service/auth_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize Supabase with your credentials
  await SupabaseConfig.initialize();
  
  runApp(MyApp());
}
```

2. Wrap your app with ProviderScope:

```dart
class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ProviderScope(
      child: MaterialApp(
        // Your app configuration
      ),
    );
  }
}
```

## Usage

### Basic Authentication Flow

```dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:auth_service/auth_service.dart';

class LoginScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authStateProvider);
    final authNotifier = ref.read(authStateProvider.notifier);

    return Scaffold(
      body: Column(
        children: [
          // Show loading state
          if (authState.isLoading)
            CircularProgressIndicator(),
          
          // Show error if any
          if (authState.error != null)
            Text(authState.error!, style: TextStyle(color: Colors.red)),
          
          // Authentication button
          ElevatedButton(
            onPressed: () async {
              final result = await authNotifier.sendMagicLink('<EMAIL>');
              
              if (result is MagicLinkSent) {
                // Show success message
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text(result.message)),
                );
              } else if (result is AuthFailure) {
                // Handle error
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text(result.message)),
                );
              }
            },
            child: Text('Send Magic Link'),
          ),
        ],
      ),
    );
  }
}
```

### Checking Authentication State

```dart
class HomeScreen extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isAuthenticated = ref.watch(isAuthenticatedProvider);
    final currentUser = ref.watch(currentUserProvider);
    
    if (!isAuthenticated) {
      return LoginScreen();
    }
    
    return Scaffold(
      appBar: AppBar(
        title: Text('Welcome ${currentUser?.name}'),
        actions: [
          IconButton(
            icon: Icon(Icons.logout),
            onPressed: () async {
              final authNotifier = ref.read(authStateProvider.notifier);
              await authNotifier.signOut();
            },
          ),
        ],
      ),
      body: Center(
        child: Text('Hello ${currentUser?.email}!'),
      ),
    );
  }
}
```

### Available Providers

- `authStateProvider`: Complete authentication state
- `currentUserProvider`: Current authenticated user
- `isAuthenticatedProvider`: Boolean authentication status
- `authLoadingProvider`: Loading state
- `authErrorProvider`: Current error message

## Architecture

### Models

- **AuthState**: Represents the current authentication state
- **AuthResult**: Sealed class for authentication operation results
  - `AuthSuccess`: Successful authentication
  - `AuthFailure`: Failed authentication with error details
  - `MagicLinkSent`: Magic link sent confirmation

### Repository

- **AuthRepository**: Handles all Supabase authentication operations
  - Magic link sending and verification
  - User profile management
  - Session management
  - Localized error handling

### State Management

- **AuthNotifier**: Riverpod StateNotifier for authentication state
- **Providers**: Various providers for different aspects of auth state

## Error Handling

The package provides comprehensive error handling with Arabic localized messages:

```dart
// Errors are automatically localized to Arabic
const errorMessages = {
  'invalid login credentials': 'بيانات تسجيل الدخول غير صحيحة',
  'email not confirmed': 'لم يتم تأكيد البريد الإلكتروني',
  'user not found': 'المستخدم غير موجود',
  // ... more error messages
};
```

## Testing

The package includes comprehensive tests:

```bash
# Run all tests
flutter test

# Run specific test suites
flutter test test/models/
flutter test test/repositories/
flutter test test/providers/
```

## Configuration

### Environment Variables

Set these environment variables for Supabase configuration:

```
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
```

### Supabase Setup

Ensure your Supabase project has:

1. Authentication enabled with magic link
2. User profiles table with proper RLS policies
3. Email templates configured for Arabic

## Contributing

1. Follow the existing code style
2. Write tests for new features
3. Update documentation
4. Ensure Arabic localization for user-facing messages

## License

This package is part of the multi-vendor ecommerce platform project.