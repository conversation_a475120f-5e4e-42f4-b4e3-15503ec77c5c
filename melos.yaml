name: multi_vendor_ecommerce

packages:
  - apps/**
  - packages/**

command:
  bootstrap:
    # It seems so that running "pub get" in parallel has some issues (like
    # https://github.com/dart-lang/pub/issues/3404). Disabling this feature
    # makes the CI much more stable.
    runPubGetInParallel: false
    usePubspecOverrides: true

  clean:
    hooks:
      pre: |
        melos exec -c 1 -- "flutter clean"

scripts:
  analyze:
    run: |
      melos exec -c 1 -- "dart analyze . --fatal-infos"
    description: |
      Run `dart analyze` in all packages.
       - Note: you can also rely on your IDEs Dart Analysis / Issues window.

  check-format:
    run: melos exec dart format --output=none --set-exit-if-changed .
    description: |
      Check if all Dart files in this repo are formatted.

  format:
    run: melos exec dart format .
    description: |
      Format all Dart files in this repo.

  test:
    run: |
      melos exec -c 1 --fail-fast -- \
        "flutter test --coverage --test-randomize-ordering-seed random"
    description: Run `flutter test` for all packages.

  test:unit:
    run: |
      melos exec -c 1 --fail-fast --dir-exists=test/unit -- \
        "flutter test test/unit --coverage --test-randomize-ordering-seed random"
    description: Run unit tests for all packages.

  test:widget:
    run: |
      melos exec -c 1 --fail-fast --dir-exists=test/widget -- \
        "flutter test test/widget --coverage --test-randomize-ordering-seed random"
    description: Run widget tests for all packages.

  test:integration:
    run: |
      melos exec -c 1 --fail-fast --dir-exists=test/integration -- \
        "flutter test test/integration --coverage --test-randomize-ordering-seed random"
    description: Run integration tests for all packages.

  build:android:
    run: |
      melos exec -c 1 --depends-on="flutter" --ignore="*web*" -- \
        "flutter build apk --release"
    description: Build Android APKs for all Flutter apps.

  get:
    run: |
      melos exec -c 5 --fail-fast -- "flutter pub get"
    description: Run `flutter pub get` in all packages.

  upgrade:
    run: |
      melos exec -c 5 --fail-fast -- "flutter pub upgrade"
    description: Run `flutter pub upgrade` in all packages.