# Design Document

## Overview

The multi-vendor ecommerce platform consists of three Flutter applications (Customer, Vendor, Courier) organized in a monorepo structure with shared components and a unified Supabase backend. The platform implements modern Flutter development practices including Riverpod for state management, Material 3 design system, TDD approach, and Arabic-first localization.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Flutter Monorepo"
        subgraph "Applications"
            CA[Customer App]
            VA[Vendor App]
            CRA[Courier App]
        end
        
        subgraph "Shared Packages"
            CORE[Core Package]
            UI[UI Components]
            AUTH[Auth Package with supabase_auth_ui]
            API[API Client]
        end
        
        CA --> CORE
        CA --> UI
        CA --> AUTH
        CA --> API
        
        VA --> CORE
        VA --> UI
        VA --> AUTH
        VA --> API
        
        CRA --> CORE
        CRA --> UI
        CRA --> AUTH
        CRA --> API
    end
    
    subgraph "Backend Services"
        SB[Supabase]
        AUTH_SVC[Auth Service]
        DB[PostgreSQL]
        STORAGE[File Storage]
        RT[Realtime]
    end
    
    API --> SB
    AUTH --> AUTH_SVC
    SB --> DB
    SB --> STORAGE
    SB --> RT
    
    subgraph "External Services"
        EMAIL[Email Service]
        MAPS[Maps Service]
    end
    
    AUTH_SVC --> EMAIL
    CRA --> MAPS
```

### Monorepo Structure

```
multi_vendor_ecommerce/
├── melos.yaml
├── pubspec.yaml
├── apps/
│   ├── customer_app/
│   │   ├── lib/
│   │   │   ├── features/
│   │   │   │   ├── auth/
│   │   │   │   ├── products/
│   │   │   │   ├── orders/
│   │   │   │   └── profile/
│   │   │   ├── l10n/
│   │   │   └── main.dart
│   │   └── pubspec.yaml
│   ├── vendor_app/
│   │   ├── lib/
│   │   │   ├── features/
│   │   │   │   ├── auth/
│   │   │   │   ├── inventory/
│   │   │   │   ├── orders/
│   │   │   │   └── analytics/
│   │   │   ├── l10n/
│   │   │   └── main.dart
│   │   └── pubspec.yaml
│   └── courier_app/
│       ├── lib/
│       │   ├── features/
│       │   │   ├── auth/
│       │   │   ├── deliveries/
│       │   │   ├── navigation/
│       │   │   └── profile/
│       │   ├── l10n/
│       │   └── main.dart
│       └── pubspec.yaml
├── packages/
│   ├── core/
│   │   ├── lib/
│   │   │   ├── models/
│   │   │   ├── enums/
│   │   │   ├── constants/
│   │   │   └── utils/
│   │   └── pubspec.yaml
│   ├── ui_components/
│   │   ├── lib/
│   │   │   ├── widgets/
│   │   │   ├── themes/
│   │   │   └── extensions/
│   │   └── pubspec.yaml
│   ├── auth_service/
│   │   ├── lib/
│   │   │   ├── providers/
│   │   │   ├── repositories/
│   │   │   ├── models/
│   │   │   └── ui/
│   │   │       └── auth_ui_wrapper.dart
│   │   └── pubspec.yaml
│   └── api_client/
│       ├── lib/
│       │   ├── clients/
│       │   ├── interceptors/
│       │   └── models/
│       └── pubspec.yaml
└── test/
    ├── integration/
    ├── widget/
    └── unit/
```

## Components and Interfaces

### Core Models

#### User Models
```dart
abstract class User {
  final String id;
  final String email;
  final String name;
  final UserType type;
  final DateTime createdAt;
  final DateTime updatedAt;
}

class Customer extends User {
  final String? phoneNumber;
  final List<Address> addresses;
  final List<String> favoriteVendors;
}

class Vendor extends User {
  final String businessName;
  final VendorCategory category;
  final String description;
  final Address businessAddress;
  final bool isVerified;
  final double rating;
}

class Courier extends User {
  final String phoneNumber;
  final String vehicleType;
  final bool isAvailable;
  final Location? currentLocation;
}
```

#### Product Models
```dart
class Product {
  final String id;
  final String vendorId;
  final String nameAr;
  final String descriptionAr;
  final double price;
  final List<ProductImage> images;
  final int stockQuantity;
  final ProductCategory category;
  final bool isActive;
  final DateTime createdAt;
}

class ProductImage {
  final String url;
  final String blurHash;
  final int width;
  final int height;
}
```

#### Order Models
```dart
class Order {
  final String id;
  final String customerId;
  final String vendorId;
  final String? courierId;
  final List<OrderItem> items;
  final double totalAmount;
  final OrderStatus status;
  final Address deliveryAddress;
  final PaymentMethod paymentMethod; // Always CASH_ON_DELIVERY
  final DateTime createdAt;
  final DateTime? deliveredAt;
}

enum OrderStatus {
  pending,
  confirmed,
  preparing,
  readyForPickup,
  pickedUp,
  inTransit,
  delivered,
  cancelled
}
```

### Authentication Architecture with supabase_auth_ui

#### Auth UI Integration
The authentication system leverages the `supabase_auth_ui` package to provide a consistent, well-tested authentication interface across all three applications. The package handles the magic link authentication flow while maintaining customization for Arabic localization and Material 3 design.

```dart
// Auth UI Wrapper for customization
class AuthUIWrapper extends StatelessWidget {
  final UserType userType;
  
  @override
  Widget build(BuildContext context) {
    return SupaEmailAuth(
      redirectTo: _getRedirectUrl(userType),
      onSignInComplete: (response) => _handleSignInComplete(response, userType),
      onSignUpComplete: (response) => _handleSignUpComplete(response, userType),
      metadataFields: [
        MetaDataField(
          prefixIcon: const Icon(Icons.person),
          label: context.l10n.fullName,
          key: 'name',
          validator: (val) => val!.isEmpty ? context.l10n.nameRequired : null,
        ),
        MetaDataField(
          prefixIcon: const Icon(Icons.business),
          label: _getUserTypeLabel(userType),
          key: 'user_type',
          initialValue: userType.name,
          readOnly: true,
        ),
      ],
      localization: SupaEmailAuthLocalization(
        enterEmail: context.l10n.enterEmail,
        enterPassword: context.l10n.enterPassword,
        forgotPassword: context.l10n.forgotPassword,
        signIn: context.l10n.signIn,
        signUp: context.l10n.signUp,
        sendMagicLink: context.l10n.sendMagicLink,
        // Additional Arabic translations
      ),
      theme: SupaEmailAuthTheme(
        primaryColor: Theme.of(context).colorScheme.primary,
        backgroundColor: Theme.of(context).colorScheme.surface,
        // Material 3 theme customization
      ),
    );
  }
}
```

#### Auth State Management Integration
```dart
class AuthNotifier extends StateNotifier<AuthState> {
  final AuthRepository _authRepository;
  final SupabaseClient _supabaseClient;
  
  AuthNotifier(this._authRepository, this._supabaseClient) : super(AuthState.initial()) {
    // Listen to auth state changes from supabase_auth_ui
    _supabaseClient.auth.onAuthStateChange.listen((data) {
      final event = data.event;
      final session = data.session;
      
      if (event == AuthChangeEvent.signedIn && session != null) {
        _handleSignedIn(session);
      } else if (event == AuthChangeEvent.signedOut) {
        _handleSignedOut();
      }
    });
  }
  
  Future<void> _handleSignedIn(Session session) async {
    try {
      final user = await _authRepository.getCurrentUser();
      state = AuthState.authenticated(user);
    } catch (e) {
      state = AuthState.error(e.toString());
    }
  }
}
```

### State Management Architecture

#### Riverpod Provider Structure
```dart
// Auth Providers
final supabaseClientProvider = Provider<SupabaseClient>((ref) {
  return Supabase.instance.client;
});

final authStateProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier(
    ref.read(authRepositoryProvider),
    ref.read(supabaseClientProvider),
  );
});

final authUIProvider = Provider<AuthUIWrapper>((ref) {
  return AuthUIWrapper();
});

// Product Providers
final productsProvider = FutureProvider.family<List<Product>, ProductFilter>((ref, filter) {
  return ref.read(productRepositoryProvider).getProducts(filter);
});

final productDetailProvider = FutureProvider.family<Product, String>((ref, productId) {
  return ref.read(productRepositoryProvider).getProduct(productId);
});

// Order Providers
final ordersProvider = StreamProvider<List<Order>>((ref) {
  final userId = ref.watch(authStateProvider).user?.id;
  if (userId == null) return Stream.value([]);
  return ref.read(orderRepositoryProvider).watchUserOrders(userId);
});
```

### Repository Pattern

#### Base Repository
```dart
abstract class BaseRepository<T> {
  Future<List<T>> getAll();
  Future<T?> getById(String id);
  Future<T> create(T entity);
  Future<T> update(T entity);
  Future<void> delete(String id);
}
```

#### Auth Repository with supabase_auth_ui Integration
```dart
class AuthRepository {
  final SupabaseClient _client;
  
  AuthRepository(this._client);
  
  // Get current user from session managed by supabase_auth_ui
  Future<User?> getCurrentUser() async {
    final session = _client.auth.currentSession;
    if (session == null) return null;
    
    final response = await _client
        .from('users')
        .select()
        .eq('id', session.user.id)
        .single();
    
    return User.fromJson(response);
  }
  
  // Handle user creation after supabase_auth_ui sign up
  Future<User> createUserProfile(Session session, Map<String, dynamic> metadata) async {
    final userData = {
      'id': session.user.id,
      'email': session.user.email,
      'name': metadata['name'],
      'user_type': metadata['user_type'],
    };
    
    final response = await _client
        .from('users')
        .insert(userData)
        .select()
        .single();
    
    return User.fromJson(response);
  }
  
  // Sign out using supabase_auth_ui
  Future<void> signOut() async {
    await _client.auth.signOut();
  }
  
  // Check if user is authenticated
  bool get isAuthenticated => _client.auth.currentSession != null;
  
  // Get auth state stream
  Stream<AuthState> get authStateChanges {
    return _client.auth.onAuthStateChange.map((data) {
      if (data.session != null) {
        return AuthState.authenticated;
      } else {
        return AuthState.unauthenticated;
      }
    });
  }
}
```

#### Specific Repositories
```dart
class ProductRepository extends BaseRepository<Product> {
  final SupabaseClient _client;
  
  Future<List<Product>> getProductsByVendor(String vendorId) async {
    // Implementation with lazy loading
  }
  
  Future<List<Product>> searchProducts(String query, {int page = 0, int limit = 20}) async {
    // Implementation with pagination
  }
}

class OrderRepository extends BaseRepository<Order> {
  Stream<List<Order>> watchUserOrders(String userId) {
    return _client
        .from('orders')
        .stream(primaryKey: ['id'])
        .eq('customer_id', userId)
        .map((data) => data.map((json) => Order.fromJson(json)).toList());
  }
}
```

## Data Models

### Supabase Database Schema

#### Tables Structure
```sql
-- Users table (base for all user types)
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR UNIQUE NOT NULL,
  name VARCHAR NOT NULL,
  user_type user_type_enum NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Customers table
CREATE TABLE customers (
  id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
  phone_number VARCHAR,
  favorite_vendors UUID[]
);

-- Vendors table
CREATE TABLE vendors (
  id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
  business_name VARCHAR NOT NULL,
  category vendor_category_enum NOT NULL,
  description TEXT,
  business_address JSONB NOT NULL,
  is_verified BOOLEAN DEFAULT FALSE,
  rating DECIMAL(3,2) DEFAULT 0.00
);

-- Couriers table
CREATE TABLE couriers (
  id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
  phone_number VARCHAR NOT NULL,
  vehicle_type VARCHAR NOT NULL,
  is_available BOOLEAN DEFAULT TRUE,
  current_location POINT
);

-- Products table
CREATE TABLE products (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  vendor_id UUID NOT NULL REFERENCES vendors(id) ON DELETE CASCADE,
  name_ar VARCHAR NOT NULL,
  description_ar TEXT,
  price DECIMAL(10,2) NOT NULL,
  stock_quantity INTEGER NOT NULL DEFAULT 0,
  category product_category_enum NOT NULL,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Product Images table
CREATE TABLE product_images (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
  url VARCHAR NOT NULL,
  blur_hash VARCHAR NOT NULL,
  width INTEGER NOT NULL,
  height INTEGER NOT NULL,
  sort_order INTEGER DEFAULT 0
);

-- Orders table
CREATE TABLE orders (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  customer_id UUID NOT NULL REFERENCES customers(id),
  vendor_id UUID NOT NULL REFERENCES vendors(id),
  courier_id UUID REFERENCES couriers(id),
  total_amount DECIMAL(10,2) NOT NULL,
  status order_status_enum NOT NULL DEFAULT 'pending',
  delivery_address JSONB NOT NULL,
  payment_method payment_method_enum NOT NULL DEFAULT 'cash_on_delivery',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  delivered_at TIMESTAMP WITH TIME ZONE
);

-- Order Items table
CREATE TABLE order_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
  product_id UUID NOT NULL REFERENCES products(id),
  quantity INTEGER NOT NULL,
  unit_price DECIMAL(10,2) NOT NULL,
  total_price DECIMAL(10,2) NOT NULL
);
```

#### Row Level Security (RLS) Policies
```sql
-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE vendors ENABLE ROW LEVEL SECURITY;
ALTER TABLE couriers ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;

-- Example policies
CREATE POLICY "Users can view their own data" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Vendors can manage their products" ON products
  FOR ALL USING (auth.uid() = vendor_id);

CREATE POLICY "Customers can view active products" ON products
  FOR SELECT USING (is_active = true);
```

## Error Handling

### Error Types and Handling Strategy

#### Custom Exception Classes
```dart
abstract class AppException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;
  
  const AppException(this.message, {this.code, this.originalError});
}

class NetworkException extends AppException {
  const NetworkException(String message, {String? code, dynamic originalError})
      : super(message, code: code, originalError: originalError);
}

class AuthException extends AppException {
  const AuthException(String message, {String? code, dynamic originalError})
      : super(message, code: code, originalError: originalError);
}

class AuthUIException extends AppException {
  const AuthUIException(String message, {String? code, dynamic originalError})
      : super(message, code: code, originalError: originalError);
}

class ValidationException extends AppException {
  final Map<String, String> fieldErrors;
  
  const ValidationException(String message, this.fieldErrors, {String? code})
      : super(message, code: code);
}
```

#### Global Error Handler
```dart
class GlobalErrorHandler {
  static void handleError(Object error, StackTrace stackTrace) {
    if (error is AppException) {
      _handleAppException(error);
    } else {
      _handleUnknownError(error, stackTrace);
    }
  }
  
  static void _handleAppException(AppException exception) {
    // Log error
    // Show user-friendly message in Arabic
    // Report to crash analytics if needed
  }
}
```

#### Error UI Components
```dart
class ErrorWidget extends StatelessWidget {
  final AppException exception;
  final VoidCallback? onRetry;
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Icon(Icons.error_outline, size: 64, color: Colors.red),
        SizedBox(height: 16),
        Text(
          _getLocalizedErrorMessage(exception),
          style: Theme.of(context).textTheme.bodyLarge,
          textAlign: TextAlign.center,
        ),
        if (onRetry != null) ...[
          SizedBox(height: 16),
          ElevatedButton(
            onPressed: onRetry,
            child: Text(context.l10n.retry),
          ),
        ],
      ],
    );
  }
}
```

## Testing Strategy

### Test Structure and Approach

#### Unit Tests
- **Model Tests**: Validate data model serialization/deserialization
- **Repository Tests**: Mock Supabase client and test data operations
- **Provider Tests**: Test Riverpod state management logic
- **Auth UI Tests**: Test supabase_auth_ui integration and customization
- **Utility Tests**: Test helper functions and extensions

#### Widget Tests
- **Component Tests**: Test individual UI components
- **Screen Tests**: Test complete screen functionality
- **Integration Tests**: Test feature workflows

#### Test Organization
```dart
// Example unit test
class ProductRepositoryTest {
  late MockSupabaseClient mockClient;
  late ProductRepository repository;
  
  setUp(() {
    mockClient = MockSupabaseClient();
    repository = ProductRepository(mockClient);
  });
  
  group('ProductRepository', () {
    test('should return products when getProducts is called', () async {
      // Arrange
      when(mockClient.from('products').select()).thenReturn(mockResponse);
      
      // Act
      final result = await repository.getProducts(ProductFilter());
      
      // Assert
      expect(result, isA<List<Product>>());
      expect(result.length, equals(2));
    });
  });
}
```

#### Golden Tests for UI Consistency
```dart
testWidgets('ProductCard should match golden file', (tester) async {
  await tester.pumpWidget(
    MaterialApp(
      home: ProductCard(product: mockProduct),
      localizationsDelegates: AppLocalizations.localizationsDelegates,
      supportedLocales: AppLocalizations.supportedLocales,
      locale: Locale('ar'),
    ),
  );
  
  await expectLater(
    find.byType(ProductCard),
    matchesGoldenFile('product_card.png'),
  );
});
```

### Performance Testing
- **Lazy Loading Tests**: Verify pagination and infinite scroll performance
- **BlurHash Tests**: Validate image loading optimization
- **Memory Tests**: Monitor memory usage during navigation
- **Build Tests**: Ensure fast compilation across all apps

### Security Testing
- **Authentication Tests**: Validate supabase_auth_ui magic link flow security
- **Auth UI Tests**: Test custom auth UI components and localization
- **Authorization Tests**: Test RLS policies
- **Input Validation Tests**: Prevent injection attacks
- **Data Encryption Tests**: Verify sensitive data protection

## Implementation Considerations

### Arabic Localization Implementation
- Use `flutter_localizations` package for RTL support
- Implement custom `LocalizationsDelegate` for app-specific strings
- Configure Material 3 theme for RTL layout
- Test UI components with Arabic text of varying lengths

### Performance Optimization
- Implement lazy loading for product lists and images
- Use BlurHash for smooth image transitions
- Optimize Supabase queries with proper indexing
- Implement caching strategies for frequently accessed data

### Security Best Practices
- Implement proper RLS policies in Supabase
- Validate all user inputs on both client and server
- Use secure storage for sensitive data
- Implement proper session management
- Regular security audits and dependency updates

### Scalability Considerations
- Design database schema for horizontal scaling
- Implement proper caching layers
- Use Supabase realtime features efficiently
- Plan for multi-region deployment if needed